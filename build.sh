#!/bin/bash

# 定义变量
TARGET_ENV="test"  # 默认发布到 test 环境
TARGET_HOST="ysa-ops"  # 默认目标主机
TARGET_DIR="/home/<USER>/ysa-auth"  # 默认目标目录
BINARY_NAME="ysa-auth"  # 编译后的二进制文件名

# 选择发布环境
echo "请选择发布环境："
echo "1. test 环境 (ysa-ops)"
echo "2. dev 环境 (ysa-dev)"
echo "3. pro 环境 (ysa)"
read -p "请输入数字 (默认: 1): " env_choice

case $env_choice in
    3)
        TARGET_ENV="pro"
        TARGET_HOST="ysa"
        ;;
    2)
        TARGET_ENV="dev"
        TARGET_HOST="ysa-dev"
        TARGET_DIR="/home/<USER>/ysa-auth"
        ;;
    *)
        TARGET_ENV="test"
        TARGET_HOST="ysa-ops"
        ;;
esac

echo "正在发布到 $TARGET_ENV 环境 ($TARGET_HOST)..."

# 编译
echo "正在编译..."
CGO_ENABLED=0 GOOS=linux GOARCH=amd64 go build -o $BINARY_NAME
if [ $? -ne 0 ]; then
    echo "编译失败！"
    exit 1
fi
echo "编译完成。"

# 上传
echo "正在上传到 $TARGET_HOST..."
scp -rp configs $BINARY_NAME $TARGET_HOST:$TARGET_DIR
if [ $? -ne 0 ]; then
    echo "上传失败！"
    exit 1
fi
echo "上传完成。"

echo "发布成功！"
