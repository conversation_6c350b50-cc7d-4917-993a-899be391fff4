# 门店菜单功能实现文档

## 概述

本文档描述了门店菜单功能的实现，当登录用户的 `tenant_type=store` 时，系统将使用 `store_menu` 表来获取角色菜单，而不是默认的 `menu` 表。

## 数据库表结构

### store_menu 表
```sql
CREATE TABLE `store_menu` (
  `menu_id` bigint NOT NULL AUTO_INCREMENT COMMENT '菜单ID',
  `menu_name` varchar(50) NOT NULL COMMENT '菜单名称',
  `parent_id` bigint DEFAULT '0' COMMENT '父菜单ID',
  `order_num` int DEFAULT '0' COMMENT '显示顺序',
  `path` varchar(200) DEFAULT '' COMMENT '路由地址',
  `component` varchar(255) DEFAULT NULL COMMENT '组件路径',
  `query` varchar(255) DEFAULT NULL COMMENT '路由参数',
  `route_name` varchar(50) DEFAULT '' COMMENT '路由名称',
  `is_frame` int DEFAULT '1' COMMENT '是否为外链（0是 1否）',
  `is_cache` int DEFAULT '0' COMMENT '是否缓存（0缓存 1不缓存）',
  `menu_type` char(1) DEFAULT '' COMMENT '菜单类型（M目录 C菜单 F按钮）',
  `visible` char(1) DEFAULT '0' COMMENT '菜单状态（0显示 1隐藏）',
  `status` char(1) DEFAULT '0' COMMENT '菜单状态（0正常 1停用）',
  `perms` varchar(100) DEFAULT NULL COMMENT '权限标识',
  `icon` varchar(100) DEFAULT '#' COMMENT '菜单图标',
  `create_by` varchar(64) NOT NULL DEFAULT '' COMMENT '创建者',
  `create_time` datetime NOT NULL COMMENT '创建时间',
  `update_by` varchar(64) NOT NULL DEFAULT '' COMMENT '更新者',
  `update_time` datetime NOT NULL COMMENT '更新时间',
  `remark` varchar(500) DEFAULT '' COMMENT '备注',
  PRIMARY KEY (`menu_id`)
) ENGINE=InnoDB COMMENT='门店菜单权限表';
```

## 实现的功能

### 1. 模型层 (Model)

#### StoreMenu 模型
- 文件: `internal/model/store_menu.go`
- 功能: 定义门店菜单的数据结构
- 修改: 添加了缺失的 `TenantType` 和 `TenantId` 字段

### 2. 数据访问层 (DAO)

#### 新增方法
- 文件: `internal/dao/menu.go`

1. **GetStoreMenus()** - 获取所有门店菜单
   ```go
   func (d *Dao) GetStoreMenus(menuType []string, fields []string) ([]*model.StoreMenu, error)
   ```

2. **GetStoreMenusByUserId()** - 根据用户ID获取门店菜单
   ```go
   func (d *Dao) GetStoreMenusByUserId(userId uint64, menuType []string, tenantType string, tenantId uint64, fields []string) ([]*model.StoreMenu, error)
   ```

### 3. 服务层 (Service)

#### 修改的方法
- 文件: `internal/service/routers.go`

1. **GetRouters()** - 主入口方法
   - 根据用户的 `tenant_type` 选择不同的菜单获取策略
   - 如果是 `store` 类型，调用 `getStoreRouters()`
   - 否则调用 `getRegularRouters()`

2. **getStoreRouters()** - 获取门店菜单路由
   - 专门处理门店用户的菜单获取逻辑
   - 支持管理员和普通用户两种模式

3. **getRegularRouters()** - 获取普通菜单路由
   - 处理非门店用户的菜单获取逻辑
   - 保持原有的业务逻辑不变

4. **convertStoreMenusToMenus()** - 门店菜单转换
   - 将 `StoreMenu` 转换为 `Menu` 格式
   - 复用现有的菜单树构建逻辑

5. **buildMenuTree()** - 构建菜单树
   - 提取的公共方法，用于构建菜单树结构

## 使用流程

### 1. 用户登录
当用户登录时，系统会获取用户的 `tenant_type` 信息。

### 2. 获取路由菜单
调用 `/getRouters` API 时：

1. 系统检查用户的 `tenant_type`
2. 如果是 `store`，使用 `store_menu` 表
3. 如果是其他类型，使用 `menu` 表

### 3. 菜单权限控制
- 门店菜单同样通过 `role_menu` 表进行权限控制
- 管理员用户可以看到所有门店菜单
- 普通用户只能看到分配给其角色的门店菜单

## 配置说明

### 租户类型
系统支持以下租户类型：
- `platform` - 平台
- `partner` - 合伙人  
- `store` - 门店

### 菜单类型
- `M` - 目录
- `C` - 菜单
- `F` - 按钮

## 测试

创建了测试文件 `test_store_menu.go` 来验证门店菜单功能：

```bash
go run test_store_menu.go
```

## 注意事项

1. **数据一致性**: 确保 `store_menu` 表中的数据格式与 `menu` 表保持一致
2. **权限控制**: 门店菜单的权限控制仍然通过 `role_menu` 表实现
3. **向后兼容**: 非门店用户的功能保持不变
4. **性能考虑**: 使用 JOIN 查询替代 Preload，提高查询性能

## 扩展性

该实现具有良好的扩展性：
- 可以轻松添加其他租户类型的专用菜单表
- 菜单转换逻辑可以复用
- 权限控制机制保持统一
