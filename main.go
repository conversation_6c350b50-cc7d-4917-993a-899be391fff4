package main

import (
	"context"
	"flag"
	"fmt"
	"net/http"
	"os"
	"os/signal"
	"path/filepath"
	"syscall"
	"time"
	"ysa-auth/global"
	"ysa-auth/internal/routers"

	"github.com/fsnotify/fsnotify"
	"github.com/gin-gonic/gin"
	"github.com/prometheus/client_golang/prometheus/promhttp"
	"github.com/shrimps80/go-service-utils/cache"
	"github.com/shrimps80/go-service-utils/config"
	"github.com/shrimps80/go-service-utils/database"
	"github.com/shrimps80/go-service-utils/logger"
	"github.com/shrimps80/go-service-utils/middleware"
	"github.com/shrimps80/go-service-utils/utils"
	"go.uber.org/zap"
	"gorm.io/gorm"

	"go.opentelemetry.io/otel"
	"go.opentelemetry.io/otel/sdk/resource"
	"go.opentelemetry.io/otel/sdk/trace"
	semconv "go.opentelemetry.io/otel/semconv/v1.17.0"
)

// AppConfig 应用配置结构体，包含所有核心组件
type AppConfig struct {
	Server *http.Server
	db     *gorm.DB
	redis  *cache.Redis
	cfg    *config.Config
	log    *zap.Logger
}

// 全局变量
var (
	mode string
)

// init 初始化命令行参数
func init() {
	flag.StringVar(&mode, "mode", "dev", "运行模式 (dev/prod)")
}

// main 程序入口
func main() {
	flag.Parse()

	// 初始化应用
	app, err := initApp()
	if err != nil {
		fmt.Printf("初始化失败: %v\n", err)
		os.Exit(1)
	}

	// 启动应用服务
	if err := startServer(app); err != nil {
		app.log.Error("服务运行失败", zap.Error(err))
		os.Exit(1)
	}
}

// startServer 启动应用服务并处理优雅关闭
func startServer(app *AppConfig) error {
	// 初始化OpenTelemetry追踪提供者
	tp := initTracerProvider(app.cfg)
	defer func() {
		if err := tp.Shutdown(context.Background()); err != nil {
			app.log.Error("关闭追踪提供者失败", zap.Error(err))
		}
	}()

	// 创建用于优雅关闭的context
	ctx, cancel := context.WithTimeout(context.Background(), 10*time.Second)
	defer cancel()

	// 启动HTTP服务
	go func() {
		app.log.Info("正在启动HTTP服务", zap.String("address", app.Server.Addr))
		if err := app.Server.ListenAndServe(); err != nil && err != http.ErrServerClosed {
			app.log.Error("HTTP服务启动失败", zap.Error(err))
			fmt.Printf("============================HTTP服务启动失败，端口号%s============================\n", app.Server.Addr)
			os.Exit(1)
		}
	}()

	app.log.Info("HTTP服务启动成功...", zap.String("address", app.Server.Addr))
	fmt.Printf("============================HTTP服务启动成功，端口号%s============================\n", app.Server.Addr)
	// 等待中断信号
	quit := make(chan os.Signal, 1)
	signal.Notify(quit, syscall.SIGINT, syscall.SIGTERM)
	<-quit

	app.log.Info("正在关闭服务...")

	// 执行优雅关闭
	if err := app.shutdown(ctx); err != nil {
		app.log.Error("HTTP服务关闭过程中发生错误...", zap.Error(err))
		return err
	}

	app.log.Info("HTTP服务关闭成功...")
	return nil
}

func initTracerProvider(cfg *config.Config) *trace.TracerProvider {
	r := resource.NewWithAttributes(
		semconv.SchemaURL,
		semconv.ServiceName(global.App.Config.GetString("Server.Name")),
		semconv.ServiceVersion(global.App.Config.GetString("Server.Version")),
	)

	tp := trace.NewTracerProvider(
		trace.WithResource(r),
		trace.WithSampler(trace.AlwaysSample()),
	)

	otel.SetTracerProvider(tp)
	return tp
}

// initApp 初始化应用配置
func initApp() (*AppConfig, error) {
	app := &AppConfig{}

	// 初始化全局App实例
	global.App = &global.AppConfig{}

	// 初始化配置
	if err := app.initConfig(); err != nil {
		return nil, err
	}

	// 初始化日志
	if err := app.initLogger(); err != nil {
		return nil, err
	}

	// 初始化数据库
	if err := app.initDatabase(); err != nil {
		return nil, err
	}

	// 初始化Redis
	if err := app.initRedis(); err != nil {
		return nil, err
	}

	// 初始化HTTP服务器
	if err := app.initHTTPServer(); err != nil {
		return nil, err
	}

	return app, nil
}

// initConfig 初始化配置
func (app *AppConfig) initConfig() error {
	var err error

	// 获取当前工作目录的绝对路径（程序运行时所在路径）
	currentDir, err := os.Getwd()
	if err != nil {
		return fmt.Errorf("获取当前工作目录失败: %v", err)
	}
	// 根据mode参数选择配置文件路径
	configPath := filepath.Join(currentDir, "configs", mode)
	// configPath := fmt.Sprintf("./configs/%s", mode)

	// 配置选项
	opts := &config.Options{
		ConfigType:  "yaml",
		ConfigName:  "config",
		ConfigPaths: []string{configPath},
	}

	// 创建配置管理器
	app.cfg, err = config.New(opts)
	if err != nil {
		return fmt.Errorf("配置初始化失败: %v", err)
	}

	// 设置全局配置
	global.App.Config = app.cfg

	// 监听配置文件变化
	app.cfg.OnConfigChange(func(e fsnotify.Event) {
		app.log.Info("配置文件已更新",
			zap.String("file", e.Name),
			zap.String("operation", e.Op.String()))
		if e.Op&fsnotify.Write == fsnotify.Write {
			// 这里可以重新加载数据库或Redis连接
			app.log.Info("检测到配置变更，可以在这里重新初始化连接")
		}
	})

	// 启动配置文件监听
	app.cfg.StartWatch()

	return nil
}

// initLogger 初始化日志
func (app *AppConfig) initLogger() error {
	logConfig := &logger.Config{
		Filename:   app.cfg.GetString("Log.SavePath") + "/" + app.cfg.GetString("Log.FileName") + app.cfg.GetString("Log.FileExt"),
		MaxSize:    app.cfg.GetInt("Log.MaxSize"),
		MaxBackups: app.cfg.GetInt("Log.MaxBackups"),
		MaxAge:     app.cfg.GetInt("Log.MaxAge"),
		Compress:   app.cfg.GetBool("Log.Compress"),
		Level:      app.cfg.GetString("Log.Level"),
	}

	var err error
	app.log, err = logger.NewLogger(logConfig)
	if err != nil {
		return fmt.Errorf("日志初始化失败: %v", err)
	}

	// 设置全局日志
	global.App.Log = app.log

	return nil
}

// initDatabase 初始化数据库
func (app *AppConfig) initDatabase() error {
	s := "%s:%s@tcp(%s)/%s?charset=%s&parseTime=%v&loc=Local"
	dsn := fmt.Sprintf(s,
		app.cfg.GetString("database.Username"),
		app.cfg.GetString("database.Password"),
		app.cfg.GetString("database.Host"),
		app.cfg.GetString("database.DBName"),
		app.cfg.GetString("database.Charset"),
		app.cfg.GetBool("database.ParseTime"),
	)

	dbConfig := &database.Config{
		Type:         app.cfg.GetString("database.DBType"),
		DSN:          dsn,
		MaxIdleConns: app.cfg.GetInt("database.MaxIdleConns"),
		MaxOpenConns: app.cfg.GetInt("database.MaxOpenConns"),
		MaxLifetime:  time.Duration(app.cfg.GetInt("database.MaxLifetime")) * time.Second,
		Debug:        app.cfg.GetBool("database.debug"),
	}

	var err error
	app.db, err = database.New(dbConfig)
	if err != nil {
		app.log.Error("数据库连接失败", zap.Error(err))
		return fmt.Errorf("数据库连接失败: %v", err)
	}

	app.log.Info("数据库连接成功")
	// 设置全局数据库连接
	global.App.DB = app.db

	return nil
}

// initRedis 初始化Redis
func (app *AppConfig) initRedis() error {
	redisConfig := &cache.RedisConfig{
		Addrs:      []string{app.cfg.GetString("redis.host")},
		Password:   app.cfg.GetString("redis.password"),
		DB:         app.cfg.GetInt("redis.db"),
		PoolSize:   app.cfg.GetInt("redis.pool_size"),
		MaxRetries: 3,
		Timeout:    time.Second * 5,
	}

	var err error
	app.redis, err = cache.NewRedis(redisConfig)
	if err != nil {
		app.log.Error("Redis连接失败", zap.Error(err))
		return fmt.Errorf("Redis连接失败: %v", err)
	}

	app.log.Info("Redis连接成功")
	// 设置全局Redis连接
	global.App.Redis = app.redis

	return nil
}

// initHTTPServer 初始化HTTP服务器
func (app *AppConfig) initHTTPServer() error {
	gin.SetMode(app.cfg.GetString("Server.RunMode"))
	router := gin.Default()

	// 注册路由
	registerRoutes(router, app.log, app.cfg)

	app.Server = &http.Server{
		Addr:           ":" + app.cfg.GetString("Server.HttpPort"),
		Handler:        router,
		ReadTimeout:    time.Duration(app.cfg.GetInt("Server.ReadTimeout")) * time.Second,
		WriteTimeout:   time.Duration(app.cfg.GetInt("Server.WriteTimeout")) * time.Second,
		IdleTimeout:    time.Duration(app.cfg.GetInt("Server.IdleTimeout")) * time.Second,
		MaxHeaderBytes: 1 << 20, // 1MB
	}

	// 设置全局服务器实例
	global.App.Server = app.Server

	return nil
}

// 注册路由
func registerRoutes(r *gin.Engine, log *zap.Logger, cfg *config.Config) {
	middleware.InitValidator()

	// 注册基础中间件
	r.Use(middleware.Health())
	r.Use(middleware.Metrics())
	r.Use(middleware.Recovery(log))
	r.Use(middleware.ValidationMiddleware())

	// 配置链路追踪
	tracingCfg := middleware.DefaultTracingConfig()
	tracingCfg.ServiceName = cfg.GetString("Server.Name")
	tracingCfg.ServiceVersion = cfg.GetString("Server.Version")
	r.Use(middleware.Tracing(tracingCfg))

	// 注册路由
	routers.RegisterAPIRoutes(r)

	// 注册基础健康检查路由
	r.GET("/ping", func(c *gin.Context) {
		utils.Success(c, gin.H{"message": "pong"})
	})

	// 注册Prometheus指标路由
	r.GET("/metrics", gin.WrapH(promhttp.Handler()))
}

// shutdown 优雅关闭
func (app *AppConfig) shutdown(ctx context.Context) error {
	// 关闭HTTP服务
	if err := app.Server.Shutdown(ctx); err != nil {
		return fmt.Errorf("HTTP服务关闭失败: %v", err)
	}

	// 关闭数据库连接
	if app.db != nil {
		sqlDB, err := app.db.DB()
		if err != nil {
			return fmt.Errorf("获取数据库实例失败: %v", err)
		}
		if err := sqlDB.Close(); err != nil {
			return fmt.Errorf("数据库连接关闭失败: %v", err)
		}
	}

	// 关闭Redis连接
	if app.redis != nil {
		if err := app.redis.Close(); err != nil {
			return fmt.Errorf("Redis连接关闭失败: %v", err)
		}
	}

	return nil
}
