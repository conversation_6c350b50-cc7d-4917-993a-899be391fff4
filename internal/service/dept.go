package service

import (
	"errors"
	"strings"
	"time"
	"ysa-auth/internal/model"
	"ysa-auth/pkg/app/req"
	"ysa-auth/pkg/app/resp"
	"ysa-auth/pkg/tools"
)

func (svc *Service) buildDeptResponse(row *model.Dept) *resp.DeptResponse {
	return &resp.DeptResponse{
		Ancestors:  row.Ancestors,
		CreateBy:   row.CreateBy,
		CreateTime: tools.Time2String(row.CreateTime),
		DelFlag:    row.DelFlag,
		DeptId:     row.DeptId,
		DeptName:   row.DeptName,
		Email:      row.Email,
		Leader:     row.Leader,
		OrderNum:   row.OrderNum,
		ParentId:   row.ParentId,
		Phone:      row.Phone,
		Status:     row.Status,
		UpdateBy:   row.UpdateBy,
		UpdateTime: tools.Time2String(row.UpdateTime),
	}
}

func (svc *Service) BuildDeptTree(depts []*resp.DeptResponse) []*resp.TreeResponse {
	if len(depts) == 0 {
		return nil
	}
	tree := []*resp.TreeResponse{}
	for _, dept := range depts {
		buf := resp.TreeResponse{
			Id:       dept.DeptId,
			Pid:      dept.ParentId,
			Label:    dept.DeptName,
			Disabled: dept.Status == "1",
			Children: nil,
		}
		tree = append(tree, &buf)
	}
	return tools.BuildSliceToTree(tree, 0)
}

// GetDeptList 获取部门列表
func (svc *Service) GetDeptList(param *req.DeptListRequest) ([]*resp.DeptResponse, error) {
	fields := model.Dept{}.GetFields()
	depts, err := svc.dao.GetDeptList(param, fields)
	if err != nil {
		return nil, err
	}
	result := make([]*resp.DeptResponse, 0)
	for _, d := range depts {
		result = append(result, svc.buildDeptResponse(d))
	}
	return result, nil
}

// GetDeptListExcludeChildren 获取部门列表（排除子部门）
func (svc *Service) GetDeptListExcludeChildren(param *req.DeptListRequest, excludeId uint64) ([]*resp.DeptResponse, error) {
	fields := model.Dept{}.GetFields()
	depts, err := svc.dao.GetDeptList(param, fields)
	if err != nil {
		return nil, err
	}
	// 获取需要排除的部门ID列表
	excludeDeptIds := make([]uint64, 0)
	for _, d := range depts {
		if d.Ancestors != "" && tools.InArrayString(strings.Split(d.Ancestors, ","), tools.Uint64ToString(excludeId)) {
			excludeDeptIds = append(excludeDeptIds, d.DeptId)
		}
	}
	// 构建返回结果
	result := make([]*resp.DeptResponse, 0)
	for _, d := range depts {
		if !tools.InArrayUint64(excludeDeptIds, d.DeptId) {
			result = append(result, svc.buildDeptResponse(d))
		}
	}
	return result, nil
}

// GetDept 获取部门详情
func (svc *Service) GetDept(deptId uint64, tenantType string, tenantId uint64) (*resp.DeptResponse, error) {
	fields := model.Dept{}.GetFields()
	dept, err := svc.dao.GetDeptById(deptId, tenantType, tenantId, fields)
	if err != nil {
		return nil, err
	}
	return svc.buildDeptResponse(dept), nil
}

// CreateDept 创建部门
func (svc *Service) CreateDept(param *req.CreateDeptRequest) error {
	fields := model.Dept{}.GetFields()
	var ancestors string
	if param.ParentId > 0 {
		parentDept, err := svc.dao.GetDeptById(param.ParentId, param.TenantType, param.TenantId, fields)
		if err != nil {
			return err
		}
		ancestors = parentDept.Ancestors + "," + tools.Uint64ToString(param.ParentId)
	} else {
		ancestors = "0"
	}

	dept := &model.Dept{
		ParentId:   param.ParentId,
		Ancestors:  ancestors,
		DeptName:   param.DeptName,
		OrderNum:   param.OrderNum,
		Leader:     param.Leader,
		Phone:      param.Phone,
		Email:      param.Email,
		Status:     param.Status,
		DelFlag:    "0",
		CreateBy:   param.Operater,
		CreateTime: time.Now(),
		UpdateBy:   param.Operater,
		UpdateTime: time.Now(),
		TenantType: param.TenantType,
		TenantId:   param.TenantId,
	}
	return svc.dao.CreateDept(dept)
}

// UpdateDept 更新部门
func (svc *Service) UpdateDept(param *req.UpdateDeptRequest) error {
	fields := model.Dept{}.GetFields()
	if _, err := svc.dao.GetDeptById(param.DeptId, param.TenantType, param.TenantId, fields); err != nil {
		return err
	}

	if param.ParentId == param.DeptId {
		return errors.New("父级部门不能选择自身")
	}

	// 获取父部门信息
	var ancestors string
	if param.ParentId > 0 {
		parentDept, err := svc.dao.GetDeptById(param.ParentId, param.TenantType, param.TenantId, model.Dept{}.GetFields())
		if err != nil {
			return err
		}
		ancestors = parentDept.Ancestors + "," + tools.Uint64ToString(param.ParentId)
	} else {
		ancestors = "0"
	}

	dept := &model.Dept{
		DeptId:     param.DeptId,
		ParentId:   param.ParentId,
		Ancestors:  ancestors,
		DeptName:   param.DeptName,
		OrderNum:   param.OrderNum,
		Leader:     param.Leader,
		Phone:      param.Phone,
		Email:      param.Email,
		Status:     param.Status,
		UpdateBy:   param.Operater,
		UpdateTime: time.Now(),
	}
	return svc.dao.UpdateDept(dept)
}

// DeleteDepts 删除部门
func (svc *Service) DeleteDept(deptId uint64, tenantType string, tenantId uint64, operater string) error {
	fields := model.Dept{}.GetFields()
	if _, err := svc.dao.GetDeptById(deptId, tenantType, tenantId, fields); err != nil {
		return err
	}
	// 判断是否存在子部门
	bool, err := svc.dao.HasChildByDeptId(deptId)
	if err != nil {
		return err
	}
	if bool {
		return errors.New("存在子部门，无法删除")
	}
	dept := &model.Dept{
		DeptId:     deptId,
		DelFlag:    "2",
		UpdateBy:   operater,
		UpdateTime: time.Now(),
	}
	return svc.dao.UpdateDept(dept)
}
