package service

import (
	"errors"
	"strings"
	"ysa-auth/internal/model"
	"ysa-auth/pkg/tools"
)

func (svc *Service) CheckUserNamePassword(username, password string) (*model.User, error) {
	fields := (model.User{}).GetFields()
	user, err := svc.dao.GetUserByUserName(username, "0", fields)
	if err != nil {
		return nil, err
	}
	if !svc.checkPassword(user, password) {
		return nil, errors.New("密码错误")
	}
	return user, nil
}

func (svc *Service) CheckUserIdPassword(userId uint64, password string) (*model.User, error) {
	fields := (model.User{}).GetFields()
	user, err := svc.dao.GetUserByUserId(userId, "0", fields)
	if err != nil {
		return nil, err
	}
	if !svc.checkPassword(user, password) {
		return nil, errors.New("密码错误")
	}
	return user, nil
}

func (svc *Service) checkPassword(user *model.User, password string) bool {
	if user.UserId == 0 {
		return false
	}
	if password == "" {
		return false
	}
	pwdNew := user.UserName + password + user.Salt
	pwdNew = tools.MustEncryptString(pwdNew)
	return strings.Compare(user.Password, pwdNew) != 0
}
