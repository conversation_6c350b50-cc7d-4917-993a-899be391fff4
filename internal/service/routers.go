package service

import (
	"strings"
	"ysa-auth/internal/model"
	"ysa-auth/pkg/app/resp"
	"ysa-auth/pkg/tools"
)

func (svc *Service) GetRouters(userId uint64) ([]*resp.TreeMenuResponse, error) {
	user, err := svc.dao.GetUserByUserId(userId, "", []string{"is_admin", "tenant_type", "tenant_id"})
	if err != nil {
		return nil, err
	}
	fields := (model.Menu{}).GetFields()
	rows := make([]*model.Menu, 0)
	if user.IsAdmin == "1" {
		rows, err = svc.dao.GetMenus([]string{"M", "C"}, user.TenantType, user.TenantId, fields)
		basicRows, _ := svc.dao.GetMenuBasic([]string{"M", "C"}, user.TenantType, fields)
		rows = append(rows, basicRows...)
		//rows MenuId 去重复
		rows = tools.RemoveDuplicateRows(rows, "MenuId")

	} else {
		rows, err = svc.dao.GetMenusByUserId(userId, []string{"M", "C"}, user.TenantType, user.TenantId, fields)
	}
	// OrderNum排序
	rows = tools.SortByField(rows, "OrderNum")
	if err != nil {
		return nil, err
	}
	arr := make([]*resp.TreeMenuResponse, 0)
	childrenMap := svc.InitChildMap(rows)
	for _, menu := range rows {
		if menu.ParentId == 0 {
			newMenu := &resp.TreeMenuResponse{
				MenuId:     menu.MenuId,
				Name:       getRouteName(menu),
				Path:       getRouterPath(menu),
				Hidden:     menu.Visible == "1",
				Redirect:   "noRedirect",
				Component:  getComponent(menu),
				AlwaysShow: true,
				Meta: &resp.TreeMetaResponse{
					Title:   menu.MenuName,
					Icon:    menu.Icon,
					NoCache: menu.IsCache == 1,
					Link:    menu.Path,
				},
			}
			svc.FillAllChildren(newMenu, childrenMap)
			arr = append(arr, newMenu)
		}
	}
	return arr, nil
}

// 生成菜单=====================================================================================================
func (svc *Service) InitChildMap(menus []*model.Menu) map[uint64][]*resp.TreeMenuResponse {
	childrenMap := make(map[uint64][]*resp.TreeMenuResponse)
	for i := range menus {
		if menus[i].MenuType == "F" { //忽略按钮
			continue
		}
		//每个menu都预设子菜单项
		childrenMap[menus[i].MenuId] = make([]*resp.TreeMenuResponse, 0)
	}

	for i := range menus {
		if menus[i].MenuType == "F" { //忽略按钮
			continue
		}
		link := ""
		if strings.HasPrefix(menus[i].Path, "http") {
			link = menus[i].Path
		}
		//组织父子关系
		buf := resp.TreeMenuResponse{
			MenuId:    menus[i].MenuId,
			Name:      getRouteName(menus[i]),
			Path:      getRouterPath(menus[i]),
			Hidden:    menus[i].Visible == "1",
			Component: getComponent(menus[i]),
			Meta: &resp.TreeMetaResponse{
				Title:   menus[i].MenuName,
				Icon:    menus[i].Icon,
				NoCache: menus[i].IsCache == 1,
				Link:    link,
			},
		}
		pid := menus[i].ParentId
		childrenMap[pid] = append(childrenMap[pid], &buf)
	}
	return childrenMap
}

func (svc *Service) FillAllChildren(m *resp.TreeMenuResponse, childrenMap map[uint64][]*resp.TreeMenuResponse) {
	children := childrenMap[m.MenuId]
	if children == nil || len(children) <= 0 {
		return
	}
	for i := range children {
		m.Children = make([]*resp.TreeChildrenResponse, 0)
		for _, v := range childrenMap[m.MenuId] {
			m.Children = append(m.Children, &resp.TreeChildrenResponse{
				Name:      v.Name,
				Path:      v.Path,
				Hidden:    v.Hidden,
				Component: v.Component,
				Meta:      v.Meta,
			})
		}
		m.ChildrenMap = childrenMap[m.MenuId]
		svc.FillAllChildren(children[i], childrenMap)
	}
}

func getRouteName(m *model.Menu) string {
	if isMenuFrame(m) {
		return ""
	}
	var str string
	if m.RouteName != "" {
		str = m.RouteName
	} else {
		str = m.Path
	}
	return strings.Title(str)
}

func getRouterPath(m *model.Menu) string {
	routerPath := m.Path
	// 内链打开外网方式
	if m.ParentId != 0 && isInnerLink(m) {
		routerPath = innerLinkReplaceEach(m.Path)
	} else if m.ParentId == 0 && m.MenuType == "M" && m.IsFrame == 1 {
		routerPath = "/" + m.Path
	} else if isMenuFrame(m) {
		routerPath = "/"
	}
	return routerPath
}

func isInnerLink(m *model.Menu) bool {
	return m.IsFrame == 1 && strings.HasPrefix(m.Path, "http")
}

func innerLinkReplaceEach(path string) string {
	return strings.ReplaceAll(strings.ReplaceAll(strings.ReplaceAll(strings.ReplaceAll(path, "http://", ""), "https://", ""), "www.", ""), ":", "/")
}

func isMenuFrame(m *model.Menu) bool {
	return m.ParentId == 0 && m.MenuType == "M" && m.IsFrame == 0
}

func isParentView(m *model.Menu) bool {
	return m.ParentId != 0 && m.MenuType == "M"
}

func getComponent(m *model.Menu) string {
	component := "Layout"
	if m.Component != "" && !isMenuFrame(m) {
		component = m.Component
	} else if m.Component == "" && m.ParentId != 0 && isInnerLink(m) {
		component = "InnerLink"
	} else if m.Component == "" && isParentView(m) {
		component = "ParentView"
	}
	return component
}

// =====================================================================================================
