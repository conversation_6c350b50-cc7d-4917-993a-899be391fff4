package service

import (
	"strings"
	"ysa-auth/internal/model"
	"ysa-auth/pkg/app/resp"
	"ysa-auth/pkg/tools"
)

func (svc *Service) GetRouters(userId uint64) ([]*resp.TreeMenuResponse, error) {
	user, err := svc.dao.GetUserByUserId(userId, "", []string{"is_admin", "tenant_type", "tenant_id"})
	if err != nil {
		return nil, err
	}

	// 根据租户类型选择不同的菜单表
	if user.TenantType == "store" {
		return svc.getStoreRouters(userId, user)
	} else {
		return svc.getRegularRouters(userId, user)
	}
}

// getRegularRouters 获取普通菜单路由
func (svc *Service) getRegularRouters(userId uint64, user *model.User) ([]*resp.TreeMenuResponse, error) {
	fields := (model.Menu{}).GetFields()
	rows := make([]*model.Menu, 0)
	var err error

	if user.IsAdmin == "1" {
		rows, err = svc.dao.GetMenus([]string{"M", "C"}, user.TenantType, user.TenantId, fields)
		if err != nil {
			return nil, err
		}
		basicRows, _ := svc.dao.GetMenuBasic([]string{"M", "C"}, user.TenantType, fields)
		rows = append(rows, basicRows...)
		//rows MenuId 去重复
		rows = tools.RemoveDuplicateRows(rows, "MenuId")
	} else {
		rows, err = svc.dao.GetMenusByUserId(userId, []string{"M", "C"}, user.TenantType, user.TenantId, fields)
		if err != nil {
			return nil, err
		}
	}

	// OrderNum排序
	rows = tools.SortByField(rows, "OrderNum")
	return svc.buildMenuTree(rows), nil
}

// getStoreRouters 获取门店菜单路由
func (svc *Service) getStoreRouters(userId uint64, user *model.User) ([]*resp.TreeMenuResponse, error) {
	fields := (model.StoreMenu{}).GetFields()
	var storeMenus []*model.StoreMenu
	var err error

	if user.IsAdmin == "1" {
		storeMenus, err = svc.dao.GetStoreMenus([]string{"M", "C"}, fields)
	} else {
		storeMenus, err = svc.dao.GetStoreMenusByUserId(userId, []string{"M", "C"}, user.TenantType, user.TenantId, fields)
	}

	if err != nil {
		return nil, err
	}

	// 将 StoreMenu 转换为 Menu 格式以复用现有逻辑
	rows := svc.convertStoreMenusToMenus(storeMenus)

	// OrderNum排序
	rows = tools.SortByField(rows, "OrderNum")
	return svc.buildMenuTree(rows), nil
}

// convertStoreMenusToMenus 将门店菜单转换为普通菜单格式
func (svc *Service) convertStoreMenusToMenus(storeMenus []*model.StoreMenu) []*model.Menu {
	menus := make([]*model.Menu, 0, len(storeMenus))
	for _, sm := range storeMenus {
		menu := &model.Menu{
			MenuId:     sm.MenuId,
			MenuName:   sm.MenuName,
			ParentId:   sm.ParentId,
			OrderNum:   sm.OrderNum,
			Path:       sm.Path,
			Component:  sm.Component,
			Query:      sm.Query,
			RouteName:  sm.RouteName,
			IsFrame:    sm.IsFrame,
			IsCache:    sm.IsCache,
			MenuType:   sm.MenuType,
			Visible:    sm.Visible,
			Status:     sm.Status,
			Perms:      sm.Perms,
			Icon:       sm.Icon,
			CreateBy:   sm.CreateBy,
			CreateTime: sm.CreateTime,
			UpdateBy:   sm.UpdateBy,
			UpdateTime: sm.UpdateTime,
			Remark:     sm.Remark,
			TenantType: sm.TenantType,
			TenantId:   sm.TenantId,
		}
		menus = append(menus, menu)
	}
	return menus
}

// buildMenuTree 构建菜单树
func (svc *Service) buildMenuTree(rows []*model.Menu) []*resp.TreeMenuResponse {
	arr := make([]*resp.TreeMenuResponse, 0)
	childrenMap := svc.InitChildMap(rows)
	for _, menu := range rows {
		if menu.ParentId == 0 {
			newMenu := &resp.TreeMenuResponse{
				MenuId:     menu.MenuId,
				Name:       getRouteName(menu),
				Path:       getRouterPath(menu),
				Hidden:     menu.Visible == "1",
				Redirect:   "noRedirect",
				Component:  getComponent(menu),
				AlwaysShow: true,
				Meta: &resp.TreeMetaResponse{
					Title:   menu.MenuName,
					Icon:    menu.Icon,
					NoCache: menu.IsCache == 1,
					Link:    menu.Path,
				},
			}
			svc.FillAllChildren(newMenu, childrenMap)
			arr = append(arr, newMenu)
		}
	}
	return arr
}

// 生成菜单=====================================================================================================
func (svc *Service) InitChildMap(menus []*model.Menu) map[uint64][]*resp.TreeMenuResponse {
	childrenMap := make(map[uint64][]*resp.TreeMenuResponse)
	for i := range menus {
		if menus[i].MenuType == "F" { //忽略按钮
			continue
		}
		//每个menu都预设子菜单项
		childrenMap[menus[i].MenuId] = make([]*resp.TreeMenuResponse, 0)
	}

	for i := range menus {
		if menus[i].MenuType == "F" { //忽略按钮
			continue
		}
		link := ""
		if strings.HasPrefix(menus[i].Path, "http") {
			link = menus[i].Path
		}
		//组织父子关系
		buf := resp.TreeMenuResponse{
			MenuId:    menus[i].MenuId,
			Name:      getRouteName(menus[i]),
			Path:      getRouterPath(menus[i]),
			Hidden:    menus[i].Visible == "1",
			Component: getComponent(menus[i]),
			Meta: &resp.TreeMetaResponse{
				Title:   menus[i].MenuName,
				Icon:    menus[i].Icon,
				NoCache: menus[i].IsCache == 1,
				Link:    link,
			},
		}
		pid := menus[i].ParentId
		childrenMap[pid] = append(childrenMap[pid], &buf)
	}
	return childrenMap
}

func (svc *Service) FillAllChildren(m *resp.TreeMenuResponse, childrenMap map[uint64][]*resp.TreeMenuResponse) {
	children := childrenMap[m.MenuId]
	if children == nil || len(children) <= 0 {
		return
	}
	for i := range children {
		m.Children = make([]*resp.TreeChildrenResponse, 0)
		for _, v := range childrenMap[m.MenuId] {
			m.Children = append(m.Children, &resp.TreeChildrenResponse{
				Name:      v.Name,
				Path:      v.Path,
				Hidden:    v.Hidden,
				Component: v.Component,
				Meta:      v.Meta,
			})
		}
		m.ChildrenMap = childrenMap[m.MenuId]
		svc.FillAllChildren(children[i], childrenMap)
	}
}

func getRouteName(m *model.Menu) string {
	if isMenuFrame(m) {
		return ""
	}
	var str string
	if m.RouteName != "" {
		str = m.RouteName
	} else {
		str = m.Path
	}
	return strings.Title(str)
}

func getRouterPath(m *model.Menu) string {
	routerPath := m.Path
	// 内链打开外网方式
	if m.ParentId != 0 && isInnerLink(m) {
		routerPath = innerLinkReplaceEach(m.Path)
	} else if m.ParentId == 0 && m.MenuType == "M" && m.IsFrame == 1 {
		routerPath = "/" + m.Path
	} else if isMenuFrame(m) {
		routerPath = "/"
	}
	return routerPath
}

func isInnerLink(m *model.Menu) bool {
	return m.IsFrame == 1 && strings.HasPrefix(m.Path, "http")
}

func innerLinkReplaceEach(path string) string {
	return strings.ReplaceAll(strings.ReplaceAll(strings.ReplaceAll(strings.ReplaceAll(path, "http://", ""), "https://", ""), "www.", ""), ":", "/")
}

func isMenuFrame(m *model.Menu) bool {
	return m.ParentId == 0 && m.MenuType == "M" && m.IsFrame == 0
}

func isParentView(m *model.Menu) bool {
	return m.ParentId != 0 && m.MenuType == "M"
}

func getComponent(m *model.Menu) string {
	component := "Layout"
	if m.Component != "" && !isMenuFrame(m) {
		component = m.Component
	} else if m.Component == "" && m.ParentId != 0 && isInnerLink(m) {
		component = "InnerLink"
	} else if m.Component == "" && isParentView(m) {
		component = "ParentView"
	}
	return component
}

// =====================================================================================================
