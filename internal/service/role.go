package service

import (
	"errors"
	"fmt"
	"strings"
	"time"
	"ysa-auth/internal/model"
	"ysa-auth/pkg/app"
	"ysa-auth/pkg/app/req"
	"ysa-auth/pkg/app/resp"
	"ysa-auth/pkg/tools"
	"ysa-auth/pkg/tools/convert"

	"github.com/davecgh/go-spew/spew"
)

func (svc *Service) buildRoleResponse(rows []*model.Role) []*resp.RoleResponse {
	roleLen := len(rows)
	if roleLen == 0 {
		return nil
	}
	roleIds := make([]uint64, 0, roleLen)
	roleList := make([]*resp.RoleResponse, 0, roleLen)
	for i := range rows {
		roleIds = append(roleIds, rows[i].RoleId)
	}
	//DeptIds
	deptList, _ := svc.dao.GetRoleDeptsByRoleIds(roleIds)
	//MenuIds,Permissions
	menuList, _ := svc.dao.GetRoleMenusByRoleIds(roleIds)
	for i := range rows {
		rid := rows[i].RoleId

		var deptIds []uint64
		if _, ok := deptList[rid]; ok {
			for _, d := range deptList[rid] {
				deptIds = append(deptIds, d.DeptId)
			}
		}
		var menuIds []uint64
		var perms []string
		if _, ok := menuList[rid]; ok {
			for _, m := range menuList[rid] {
				menuIds = append(menuIds, m.MenuId)
				if len(m.Perms) > 0 {
					perms = append(perms, m.Perms)
				}
			}
		}
		roleList = append(roleList, &resp.RoleResponse{
			CreateBy:          rows[i].CreateBy,
			CreateTime:        tools.Time2String(rows[i].CreateTime),
			DataScope:         rows[i].DataScope,
			DelFlag:           rows[i].DelFlag,
			DeptCheckStrictly: rows[i].DeptCheckStrictly,
			DeptIds:           deptIds,
			MenuCheckStrictly: rows[i].MenuCheckStrictly,
			MenuIds:           menuIds,
			Permissions:       perms,
			Remark:            rows[i].Remark,
			RoleId:            rows[i].RoleId,
			RoleKey:           rows[i].RoleKey,
			RoleName:          rows[i].RoleName,
			RoleSort:          rows[i].RoleSort,
			Status:            rows[i].Status,
			UpdateBy:          rows[i].UpdateBy,
			UpdateTime:        tools.Time2String(rows[i].UpdateTime),
		})
	}
	return roleList
}

// GetRoleAll 获取所有角色
func (svc *Service) GetRoleAll(tenantType string, tenantId uint64) ([]*resp.RoleResponse, error) {
	fields := (model.Role{}).GetFields()
	rows, err := svc.dao.GetRoleAll(tenantType, tenantId, fields)
	if err != nil {
		return nil, err
	}
	return svc.buildRoleResponse(rows), nil
}

func (svc *Service) GetRoleList(param *req.RoleListRequest, pager *app.Pager) (int64, []*resp.RoleResponse, error) {
	fields := model.Role{}.GetFields()
	total, rows, err := svc.dao.GetRoleList(param, fields, pager.Page, pager.PageSize)
	if err != nil {
		return 0, nil, err
	}
	return total, svc.buildRoleResponse(rows), nil
}

func (svc *Service) GetRole(roleId uint64, tenantType string, tenantId uint64) (*resp.RoleResponse, error) {
	fields := model.Role{}.GetFields()
	role, err := svc.dao.GetRoleById(roleId, tenantType, tenantId, fields)
	if err != nil {
		return nil, err
	}
	buf := svc.buildRoleResponse([]*model.Role{role})
	if len(buf) == 0 {
		return nil, nil
	}
	return buf[0], nil
}

func (svc *Service) CreateRole(param *req.CreateRoleRequest) error {
	if svc.CheckRoleNameUnique(param.RoleName, param.TenantType, param.TenantId) {
		return errors.New("新增角色'" + param.RoleName + "'失败，角色名称已存在")
	} else if svc.CheckRoleKeyUnique(param.RoleKey, param.TenantType, param.TenantId) {
		return errors.New("新增角色'" + param.RoleName + "'失败，角色权限已存在")
	}
	if err := svc.dao.CreateRole(param); err != nil {
		return err
	}
	return nil
}

func (svc *Service) UpdateRole(param *req.UpdateRoleRequest) error {
	fields := model.Role{}.GetFields()
	existByRole, err := svc.dao.GetRoleById(param.RoleId, param.TenantType, param.TenantId, fields)
	if err != nil {
		return err
	}
	if existByRole.RoleId == 0 {
		return errors.New("角色不存在")
	}
	values := map[string]interface{}{}
	if param.RoleName != existByRole.RoleName {
		if svc.CheckRoleNameUnique(param.RoleName, param.TenantType, param.TenantId) {
			return errors.New("修改角色'" + param.RoleName + "'失败，角色名称已存在")
		}
		values["role_name"] = param.RoleName
	}
	if param.RoleKey != existByRole.RoleKey {
		if svc.CheckRoleKeyUnique(param.RoleKey, param.TenantType, param.TenantId) {
			return errors.New("修改角色'" + param.RoleName + "'失败，角色权限已存在")
		}
		values["role_key"] = param.RoleKey
	}
	if param.RoleSort != existByRole.RoleSort {
		values["role_sort"] = param.RoleSort
	}
	if param.MenuCheckStrictly != existByRole.MenuCheckStrictly {
		values["menu_check_strictly"] = param.MenuCheckStrictly
	}
	if param.DeptCheckStrictly != existByRole.DeptCheckStrictly {
		values["dept_check_strictly"] = param.DeptCheckStrictly
	}
	if existByRole.Status != param.Status {
		values["status"] = param.Status
	}
	values["update_by"] = param.Operater
	values["update_time"] = time.Now()
	if err = svc.dao.UpdateRole(existByRole.RoleId, param.TenantType, param.TenantId, values, param.MenuIds, param.DeptIds); err != nil {
		return err
	}
	return nil
}

func (svc *Service) CheckRoleNameUnique(roleName, tenantType string, tenantId uint64) bool {
	row, err := svc.dao.GetRoleByRoleName(roleName, tenantType, tenantId, []string{"role_id"})
	if err != nil {
		return false
	}
	return row.RoleId > 0
}

func (svc *Service) CheckRoleKeyUnique(roleKey string, tenantType string, tenantId uint64) bool {
	row, err := svc.dao.GetRoleByRoleKey(roleKey, tenantType, tenantId, []string{"role_id"})
	if err != nil {
		return false
	}
	return row.RoleId > 0
}

func (svc *Service) InsertAuthDataScope(param *req.AuthDataScopeRequest) error {
	fields := model.Role{}.GetFields()
	existByRole, err := svc.dao.GetRoleById(param.RoleId, param.TenantType, param.TenantId, fields)
	if err != nil {
		return err
	}
	if existByRole.RoleId == 0 {
		return errors.New("角色不存在")
	}
	dept_check_strictly := "0"
	if param.DeptCheckStrictly {
		dept_check_strictly = "1"
	}
	// 修改角色信息
	values := map[string]interface{}{
		"dept_check_strictly": dept_check_strictly,
		"data_scope":          param.DataScope,
	}
	if err = svc.dao.UpdateRole(existByRole.RoleId, param.TenantType, param.TenantId, values, "", param.DeptIds); err != nil {
		return err
	}
	return nil
}

func (svc *Service) UpdateRoleStatus(param *req.ChangeRoleStatusRequest) error {
	fields := model.Role{}.GetFields()
	existByRole, err := svc.dao.GetRoleById(param.RoleId, param.TenantType, param.TenantId, fields)
	if err != nil {
		return err
	}
	if existByRole.RoleId == 0 {
		return errors.New("角色不存在")
	}
	values := map[string]interface{}{
		"status":      param.Status,
		"update_by":   param.Operater,
		"update_time": time.Now(),
	}
	return svc.dao.UpdateRole(existByRole.RoleId, param.TenantType, param.TenantId, values, "", "")
}

func (svc *Service) DeleteRoles(ids []uint64, tenantType string, tenantId uint64, operater string) error {
	return svc.dao.DeleteRoleByIds(ids, tenantType, tenantId, operater)
}

func (svc *Service) SelectAllocatedList(param *req.SelectRoleUserRequest, pager *app.Pager) (int64, []*resp.SelectUserResponse, error) {
	fields := []string{"user_id", "dept_id", "user_name", "nick_name", "email", "phonenumber", "status", "create_time"}
	total, rows, err := svc.dao.GetUserListByRoleId(param, fields, pager.Page, pager.PageSize)
	if err != nil {
		return 0, nil, err
	}
	res := make([]*resp.SelectUserResponse, 0, len(rows))
	for _, v := range rows {
		res = append(res, &resp.SelectUserResponse{
			UserId:      v.UserId,
			DeptId:      v.DeptId,
			UserName:    v.UserName,
			NickName:    v.NickName,
			Email:       v.Email,
			Phonenumber: v.Phonenumber,
			Status:      v.Status,
			CreateTime:  v.CreateTime,
		})
	}
	return total, res, nil
}

func (svc *Service) SelectUnallocatedList(param *req.SelectRoleUserRequest, pager *app.Pager) (int64, []*resp.SelectUserResponse, error) {
	fields := []string{"user_id", "dept_id", "user_name", "nick_name", "email", "phonenumber", "status", "create_time"}
	total, rows, err := svc.dao.GetUserListByRoleIdExcludeUserIds(param, fields, pager.Page, pager.PageSize)
	if err != nil {
		return 0, nil, err
	}
	res := make([]*resp.SelectUserResponse, 0, len(rows))
	for _, v := range rows {
		res = append(res, &resp.SelectUserResponse{
			UserId:      v.UserId,
			DeptId:      v.DeptId,
			UserName:    v.UserName,
			NickName:    v.NickName,
			Email:       v.Email,
			Phonenumber: v.Phonenumber,
			Status:      v.Status,
			CreateTime:  v.CreateTime,
		})
	}
	return total, res, nil
}

func (svc *Service) GetRoleDeptIds(roleId uint64) []uint64 {
	DeptList, _ := svc.dao.GetRoleDeptsByRoleIds([]uint64{roleId})
	deptIds := make([]uint64, 0, len(DeptList))
	for _, dept := range DeptList[roleId] {
		deptIds = append(deptIds, dept.DeptId)
	}
	return deptIds
}

func (svc *Service) GetRoleMenuIds(roleId uint64) []uint64 {
	menuList, _ := svc.dao.GetRoleMenusByRoleIds([]uint64{roleId})
	spew.Dump(menuList)
	menuIds := make([]uint64, 0, len(menuList))
	for _, menu := range menuList[roleId] {
		menuIds = append(menuIds, menu.MenuId)
	}
	return menuIds
}

// DeleteAuthUsers 删除用户角色
func (svc *Service) DeleteAuthUsers(roleId uint64, tenantType string, tenantId uint64, userIds []uint64) error {
	if len(userIds) == 0 {
		return nil
	}
	if len(userIds) == 1 {
		return svc.dao.DeleteByRoleAndUser(userIds[0], roleId, tenantType, tenantId)
	}
	return svc.dao.DeleteByRoleAndUsers(roleId, userIds, tenantType, tenantId)
}

// InsertAuthUsers 新增用户角色
func (svc *Service) InsertAuthUsers(param *req.AuthUsersRequest) error {
	// 根据角色查找关联userid
	userIds, err := svc.dao.GetUserIdsByRoleId(param.RoleId, param.TenantType, param.TenantId)
	if err != nil {
		return err
	}
	var userIdList []uint64
	if param.UserIds != "" {
		buf := strings.Split(param.UserIds, ",")
		for _, v := range buf {
			userIdList = append(userIdList, convert.StrTo(v).MustUInt64())
		}
	}
	if len(userIdList) == 0 {
		return nil
	}
	exist := make([]uint64, 0, len(userIds))
	rows := make([]string, 0, len(userIdList))
	for _, userId := range userIdList {
		if len(userIds) > 0 && tools.InArrayUint64(userIds, userId) {
			continue
		}
		if len(exist) > 0 && tools.InArrayUint64(exist, userId) {
			continue
		} else {
			exist = append(exist, userId)
		}
		row := fmt.Sprintf("%d,%d", userId, param.RoleId)
		rows = append(rows, row)
	}
	return svc.dao.BatchUserRole(rows, param.TenantType, param.TenantId)
}
