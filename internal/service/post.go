package service

import (
	"errors"
	"time"
	"ysa-auth/internal/model"
	"ysa-auth/pkg/app"
	"ysa-auth/pkg/app/req"
	"ysa-auth/pkg/app/resp"
	"ysa-auth/pkg/tools"
)

// GetPostAll 获取所有岗位
func (svc *Service) GetPostAll(tenantType string, tenantId uint64) ([]*resp.PostResponse, error) {
	fields := (model.Post{}).GetFields()
	rows, err := svc.dao.GetPostAll(fields, tenantType, tenantId)
	if err != nil {
		return nil, err
	}
	res := make([]*resp.PostResponse, 0, len(rows))
	for _, row := range rows {
		res = append(res, &resp.PostResponse{
			PostId:     row.PostId,
			PostCode:   row.PostCode,
			PostName:   row.PostName,
			PostSort:   row.PostSort,
			Status:     row.Status,
			CreateBy:   row.CreateBy,
			CreateTime: tools.Time2String(row.CreateTime),
			UpdateBy:   row.UpdateBy,
			UpdateTime: tools.Time2String(row.UpdateTime),
			Remark:     row.Remark,
		})
	}
	return res, nil
}

func (svc *Service) GetPostList(param *req.PostListRequest, pager *app.Pager) (int64, []*resp.PostResponse, error) {
	fields := (model.Post{}).GetFields()
	rows, total, err := svc.dao.GetPostList(param, fields, pager.Page, pager.PageSize)
	if err != nil {
		return 0, nil, err
	}
	res := make([]*resp.PostResponse, 0, len(rows))
	for _, row := range rows {
		res = append(res, &resp.PostResponse{
			PostId:     row.PostId,
			PostCode:   row.PostCode,
			PostName:   row.PostName,
			PostSort:   row.PostSort,
			Status:     row.Status,
			CreateBy:   row.CreateBy,
			CreateTime: tools.Time2String(row.CreateTime),
			UpdateBy:   row.UpdateBy,
			UpdateTime: tools.Time2String(row.UpdateTime),
			Remark:     row.Remark,
		})
	}
	return total, res, nil
}

func (svc *Service) GetPost(postId uint64, tenantType string, tenantId uint64) (*resp.PostResponse, error) {
	fields := (model.Post{}).GetFields()
	row, err := svc.dao.GetPostById(postId, tenantType, tenantId, fields)
	if err != nil {
		return nil, err
	}
	return &resp.PostResponse{
		PostId:     row.PostId,
		PostCode:   row.PostCode,
		PostName:   row.PostName,
		PostSort:   row.PostSort,
		Status:     row.Status,
		CreateBy:   row.CreateBy,
		CreateTime: tools.Time2String(row.CreateTime),
		UpdateBy:   row.UpdateBy,
		UpdateTime: tools.Time2String(row.UpdateTime),
		Remark:     row.Remark,
	}, nil
}

func (svc *Service) CreatePost(param *req.CreatePostRequest) error {
	if svc.CheckPostCodeUniqueForPost(param.PostCode, param.TenantType, param.TenantId) {
		return errors.New("新增岗位'" + param.PostName + "'失败，岗位编码已存在")
	} else if svc.CheckPostNameUniqueForPost(param.PostName, param.TenantType, param.TenantId) {
		return errors.New("新增岗位'" + param.PostName + "'失败，岗位名称已存在")
	}
	if err := svc.dao.CreatePost(param); err != nil {
		return err
	}
	return nil
}

func (svc *Service) UpdatePost(param *req.UpdatePostRequest) error {
	fields := (model.Post{}).GetFields()
	existByPost, err := svc.dao.GetPostById(param.PostId, param.TenantType, param.TenantId, fields)
	if err != nil {
		return err
	}
	if existByPost.PostId == 0 {
		return errors.New("岗位不存在")
	}
	values := map[string]interface{}{}
	if param.PostCode != existByPost.PostCode {
		if svc.CheckPostCodeUniqueForPost(param.PostCode, param.TenantType, param.TenantId) {
			return errors.New("修改岗位'" + param.PostName + "'失败，岗位编码已存在")
		}
		values["post_code"] = param.PostCode
	}
	if param.PostName != existByPost.PostName {
		if svc.CheckPostNameUniqueForPost(param.PostName, param.TenantType, param.TenantId) {
			return errors.New("修改岗位'" + param.PostName + "'失败，岗位名称已存在")
		}
		values["post_name"] = param.PostName
	}
	if param.PostSort != existByPost.PostSort {
		values["post_sort"] = param.PostSort
	}
	if param.Status != existByPost.Status {
		values["status"] = param.Status
	}
	if param.Remark != existByPost.Remark {
		values["remark"] = param.Remark
	}
	values["update_by"] = param.Operater
	values["update_time"] = time.Now()
	if err = svc.dao.UpdatePost(existByPost.PostId, values); err != nil {
		return err
	}
	return nil
}

func (svc *Service) CheckPostCodeUniqueForPost(postCode, tenantType string, tenantId uint64) bool {
	row, err := svc.dao.GetPostByPostCode(postCode, tenantType, tenantId, []string{"post_id"})
	if err != nil {
		return false
	}
	return row.PostId > 0
}

func (svc *Service) CheckPostNameUniqueForPost(postName, tenantType string, tenantId uint64) bool {
	row, err := svc.dao.GetPostByPostName(postName, tenantType, tenantId, []string{"post_id"})
	if err != nil {
		return false
	}
	return row.PostId > 0
}

func (svc *Service) DeletePosts(ids []uint64) error {
	return svc.dao.DeletePosts(ids)
}
