package service

import (
	"errors"
	"strconv"
	"strings"
	"time"
	"ysa-auth/internal/model"
	"ysa-auth/pkg/app/req"
	"ysa-auth/pkg/app/resp"
	"ysa-auth/pkg/tools"
	"ysa-auth/pkg/tools/convert"
)

func (svc *Service) buildMenuResponse(row *model.Menu) *resp.MenuResponse {
	return &resp.MenuResponse{
		CreateTime: tools.Time2String(row.CreateTime),
		Remark:     row.Remark,
		MenuId:     row.MenuId,
		MenuName:   row.MenuName,
		ParentId:   row.ParentId,
		OrderNum:   row.OrderNum,
		Path:       row.Path,
		Component:  row.Component,
		Query:      row.Query,
		RouteName:  row.RouteName,
		IsFrame:    strconv.Itoa(row.IsFrame),
		IsCache:    strconv.Itoa(row.IsCache),
		MenuType:   row.MenuType,
		Visible:    row.Visible,
		Status:     row.Status,
		Perms:      row.Perms,
		Icon:       row.Icon,
	}
}

func (svc *Service) BuildMenuTree(menus []*resp.MenuResponse) []*resp.TreeResponse {
	if len(menus) == 0 {
		return nil
	}
	tree := []*resp.TreeResponse{}
	for _, menu := range menus {
		buf := resp.TreeResponse{
			Id:       menu.MenuId,
			Pid:      menu.ParentId,
			Label:    menu.MenuName,
			Disabled: menu.Status == "1",
			Children: nil,
		}
		tree = append(tree, &buf)
	}
	return tools.BuildSliceToTree(tree, 0)
}

func (svc *Service) GetMenuList(param *req.MenuListRequest) ([]*resp.MenuResponse, error) {
	fields := model.Menu{}.GetFields()
	menus, err := svc.dao.GetMenuList(param, fields)
	if err != nil {
		return nil, err
	}
	res := make([]*resp.MenuResponse, 0, len(menus))
	for _, menu := range menus {
		res = append(res, svc.buildMenuResponse(menu))
	}
	return res, err
}

func (svc *Service) GetMenuByMenuId(id uint64, tenantType string, tenantId uint64) (*resp.MenuResponse, error) {
	fields := model.Menu{}.GetFields()
	row, err := svc.dao.GetMenuById(id, tenantType, tenantId, fields)
	if err != nil {
		return nil, err
	}
	return svc.buildMenuResponse(row), nil
}

func (svc *Service) GetMenuIdsByRoleId(roleId uint64) []uint64 {
	menuList, _ := svc.dao.GetRoleMenusByRoleIds([]uint64{roleId})
	menuIds := make([]uint64, 0, len(menuList))
	for i := range menuList[roleId] {
		menuIds = append(menuIds, menuList[roleId][i].MenuId)
	}
	return menuIds
}

func (svc *Service) CreateMenu(param *req.CreateMenuRequest) error {
	if svc.CheckMenuNameUnique(param.MenuName, param.TenantType, param.TenantId) {
		return errors.New("新增菜单'" + param.MenuName + "'失败，菜单名称已存在")
	}
	if param.IsFrame == "0" && !strings.HasPrefix(param.Path, "http") {
		return errors.New("新增菜单'" + param.MenuName + "'失败，外链必须以http(s)://开头")
	}
	if err := svc.dao.CreateMenu(param); err != nil {
		return err
	}
	return nil
}

func (svc *Service) UpdateMenu(param *req.UpdateMenuRequest) error {
	fields := model.Menu{}.GetFields()
	existByMenu, err := svc.dao.GetMenuById(param.MenuId, param.TenantType, param.TenantId, fields)
	if err != nil {
		return err
	}
	if existByMenu.MenuId == 0 {
		return errors.New("菜单不存在")
	}

	values := map[string]interface{}{}
	if param.MenuName != existByMenu.MenuName {
		if svc.CheckMenuNameUnique(param.MenuName, param.TenantType, param.TenantId) {
			return errors.New("修改菜单'" + param.MenuName + "'失败，菜单名称已存在")
		}
		values["menu_name"] = param.MenuName
	}

	if param.ParentId != existByMenu.ParentId {
		if existByMenu.MenuId == param.ParentId {
			return errors.New("修改菜单'" + param.MenuName + "'失败，上级菜单不能选择自己")
		}
		values["parent_id"] = param.ParentId
	}
	if param.OrderNum != existByMenu.OrderNum {
		values["order_num"] = param.OrderNum
	}
	if param.Path != existByMenu.Path {
		values["path"] = param.Path
	}
	if param.Component != existByMenu.Component {
		values["component"] = param.Component
	}
	if param.Query != existByMenu.Query {
		values["query"] = param.Query
	}
	if param.RouteName != existByMenu.RouteName {
		values["route_name"] = param.RouteName
	}
	isFrame := convert.StrTo(param.IsFrame).MustInt()
	if isFrame != existByMenu.IsFrame {
		if isFrame == 0 && !strings.HasPrefix(param.Path, "http") {
			return errors.New("修改菜单'" + param.MenuName + "'失败，外链必须以http(s)://开头")
		}
		values["is_frame"] = isFrame
	}
	isCache := convert.StrTo(param.IsCache).MustInt()
	if isCache != existByMenu.IsCache {
		values["is_cache"] = isCache
	}
	if param.MenuType != existByMenu.MenuType {
		values["menu_type"] = param.MenuType
	}
	if param.Visible != existByMenu.Visible {
		values["visible"] = param.Visible
	}
	if param.Status != existByMenu.Status {
		values["status"] = param.Status
	}
	if param.Icon != existByMenu.Icon {
		values["icon"] = param.Icon
	}
	if param.Perms != existByMenu.Perms {
		values["perms"] = param.Perms
	}
	if param.Remark != "" {
		values["remark"] = param.Remark
	}
	values["update_by"] = param.Operater
	values["update_time"] = time.Now()
	if err = svc.dao.UpdateMenu(existByMenu.MenuId, param.TenantType, param.TenantId, values); err != nil {
		return err
	}
	return nil
}

func (svc *Service) CheckMenuNameUnique(menuName string, tenantType string, tenantId uint64) bool {
	row, err := svc.dao.GetMenuByMenuName(menuName, tenantType, tenantId, []string{"menu_id"})
	if err != nil {
		return false
	}
	return row.MenuId > 0
}

func (svc *Service) DeleteMenu(id uint64, tenantType string, tenantId uint64) error {
	if svc.HasChildByMenuId(id, tenantType, tenantId) {
		return errors.New("存在子菜单,不允许删除")
	}
	if svc.CheckMenuExistRole(id, tenantType, tenantId) {
		return errors.New("菜单已分配,不允许删除")
	}
	return svc.dao.DeleteMenu(id, tenantType, tenantId)
}

func (svc *Service) HasChildByMenuId(id uint64, tenantType string, tenantId uint64) bool {
	parent, err := svc.dao.GetMenuListByParentId(id, tenantType, tenantId, []string{"menu_id"})
	if err != nil {
		return false
	}
	return len(parent) > 0
}

func (svc *Service) CheckMenuExistRole(id uint64, tenantType string, tenantId uint64) bool {
	roleIds, err := svc.dao.GetRoleIdsByMenuId(id, tenantType, tenantId)
	if err != nil {
		return false
	}
	return len(roleIds) > 0
}

// ==================== 门店菜单相关方法 ====================

func (svc *Service) buildStoreMenuResponse(row *model.StoreMenu) *resp.StoreMenuResponse {
	return &resp.StoreMenuResponse{
		CreateTime: tools.Time2String(row.CreateTime),
		Remark:     row.Remark,
		MenuId:     row.MenuId,
		MenuName:   row.MenuName,
		ParentId:   row.ParentId,
		OrderNum:   row.OrderNum,
		Path:       row.Path,
		Component:  row.Component,
		Query:      row.Query,
		RouteName:  row.RouteName,
		IsFrame:    strconv.Itoa(row.IsFrame),
		IsCache:    strconv.Itoa(row.IsCache),
		MenuType:   row.MenuType,
		Visible:    row.Visible,
		Status:     row.Status,
		Perms:      row.Perms,
		Icon:       row.Icon,
	}
}

func (svc *Service) BuildStoreMenuTree(menus []*resp.StoreMenuResponse) []*resp.TreeResponse {
	if len(menus) == 0 {
		return nil
	}
	tree := []*resp.TreeResponse{}
	for _, menu := range menus {
		buf := resp.TreeResponse{
			Id:       menu.MenuId,
			Pid:      menu.ParentId,
			Label:    menu.MenuName,
			Disabled: menu.Status == "1",
			Children: nil,
		}
		tree = append(tree, &buf)
	}
	return tools.BuildSliceToTree(tree, 0)
}

func (svc *Service) GetStoreMenuList(param *req.StoreMenuListRequest) ([]*resp.StoreMenuResponse, error) {
	fields := model.StoreMenu{}.GetFields()
	menus, err := svc.dao.GetStoreMenuList(param, fields)
	if err != nil {
		return nil, err
	}
	res := make([]*resp.StoreMenuResponse, 0, len(menus))
	for _, menu := range menus {
		res = append(res, svc.buildStoreMenuResponse(menu))
	}
	return res, err
}

func (svc *Service) GetStoreMenuByMenuId(id uint64) (*resp.StoreMenuResponse, error) {
	fields := model.StoreMenu{}.GetFields()
	row, err := svc.dao.GetStoreMenuById(id, fields)
	if err != nil {
		return nil, err
	}
	return svc.buildStoreMenuResponse(row), nil
}

func (svc *Service) CreateStoreMenu(param *req.CreateStoreMenuRequest) error {
	if svc.CheckStoreMenuNameUnique(param.MenuName) {
		return errors.New("新增门店菜单'" + param.MenuName + "'失败，菜单名称已存在")
	}
	if param.IsFrame == "0" && !strings.HasPrefix(param.Path, "http") {
		return errors.New("新增门店菜单'" + param.MenuName + "'失败，外链必须以http(s)://开头")
	}
	if err := svc.dao.CreateStoreMenu(param); err != nil {
		return err
	}
	return nil
}

func (svc *Service) UpdateStoreMenu(param *req.UpdateStoreMenuRequest) error {
	fields := model.StoreMenu{}.GetFields()
	existByMenu, err := svc.dao.GetStoreMenuById(param.MenuId, fields)
	if err != nil {
		return err
	}
	if existByMenu.MenuId == 0 {
		return errors.New("门店菜单不存在")
	}

	values := map[string]interface{}{}
	if param.MenuName != existByMenu.MenuName {
		if svc.CheckStoreMenuNameUnique(param.MenuName) {
			return errors.New("修改门店菜单'" + param.MenuName + "'失败，菜单名称已存在")
		}
		values["menu_name"] = param.MenuName
	}

	if param.ParentId != existByMenu.ParentId {
		if existByMenu.MenuId == param.ParentId {
			return errors.New("修改门店菜单'" + param.MenuName + "'失败，上级菜单不能选择自己")
		}
		values["parent_id"] = param.ParentId
	}
	if param.OrderNum != existByMenu.OrderNum {
		values["order_num"] = param.OrderNum
	}
	if param.Path != existByMenu.Path {
		values["path"] = param.Path
	}
	if param.Component != existByMenu.Component {
		values["component"] = param.Component
	}
	if param.Query != existByMenu.Query {
		values["query"] = param.Query
	}
	if param.RouteName != existByMenu.RouteName {
		values["route_name"] = param.RouteName
	}
	isFrame := convert.StrTo(param.IsFrame).MustInt()
	if isFrame != existByMenu.IsFrame {
		if isFrame == 0 && !strings.HasPrefix(param.Path, "http") {
			return errors.New("修改门店菜单'" + param.MenuName + "'失败，外链必须以http(s)://开头")
		}
		values["is_frame"] = isFrame
	}
	isCache := convert.StrTo(param.IsCache).MustInt()
	if isCache != existByMenu.IsCache {
		values["is_cache"] = isCache
	}
	if param.MenuType != existByMenu.MenuType {
		values["menu_type"] = param.MenuType
	}
	if param.Visible != existByMenu.Visible {
		values["visible"] = param.Visible
	}
	if param.Status != existByMenu.Status {
		values["status"] = param.Status
	}
	if param.Icon != existByMenu.Icon {
		values["icon"] = param.Icon
	}
	if param.Perms != existByMenu.Perms {
		values["perms"] = param.Perms
	}
	if param.Remark != "" {
		values["remark"] = param.Remark
	}
	values["update_by"] = param.Operater
	values["update_time"] = time.Now()
	if err = svc.dao.UpdateStoreMenu(existByMenu.MenuId, values); err != nil {
		return err
	}
	return nil
}

func (svc *Service) CheckStoreMenuNameUnique(menuName string) bool {
	row, err := svc.dao.GetStoreMenuByMenuName(menuName, []string{"menu_id"})
	if err != nil {
		return false
	}
	return row.MenuId > 0
}

func (svc *Service) DeleteStoreMenu(id uint64) error {
	if svc.HasChildByStoreMenuId(id) {
		return errors.New("存在子菜单,不允许删除")
	}
	if svc.CheckStoreMenuExistRole(id) {
		return errors.New("菜单已分配,不允许删除")
	}
	return svc.dao.DeleteStoreMenu(id)
}

func (svc *Service) HasChildByStoreMenuId(id uint64) bool {
	parent, err := svc.dao.GetStoreMenuListByParentId(id, []string{"menu_id"})
	if err != nil {
		return false
	}
	return len(parent) > 0
}

func (svc *Service) CheckStoreMenuExistRole(id uint64) bool {
	// 门店菜单也通过 role_menu 表进行权限控制
	// 这里检查是否有角色关联了这个门店菜单
	roleIds, err := svc.dao.GetRoleIdsByMenuId(id, "", 0) // 门店菜单不区分租户
	if err != nil {
		return false
	}
	return len(roleIds) > 0
}
