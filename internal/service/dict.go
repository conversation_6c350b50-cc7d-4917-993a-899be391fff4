package service

import (
	"errors"
	"time"
	"ysa-auth/internal/model"
	"ysa-auth/pkg/app"
	"ysa-auth/pkg/app/req"
	"ysa-auth/pkg/app/resp"
	"ysa-auth/pkg/tools"
)

func (svc *Service) GetDictTypeList(param *req.DictTypeListRequest, pager *app.Pager) (int64, []*resp.DictTypeResponse, error) {
	fields := (model.DictType{}).GetFields()
	pageOption := &app.PageOption{
		Page:      pager.Page,
		PageSize:  pager.PageSize,
		NeedTotal: true,
	}
	rows, total, err := svc.dao.GetDictTypeList(param, fields, pageOption)
	if err != nil {
		return 0, nil, err
	}
	res := make([]*resp.DictTypeResponse, 0, len(rows))
	for _, row := range rows {
		res = append(res, &resp.DictTypeResponse{
			DictId:     row.DictId,
			DictName:   row.DictName,
			DictType:   row.DictType,
			Status:     row.Status,
			CreateBy:   row.CreateBy,
			CreateTime: tools.Time2String(row.CreateTime),
			UpdateBy:   row.UpdateBy,
			UpdateTime: tools.Time2String(row.UpdateTime),
			Remark:     row.Remark,
		})
	}
	return total, res, nil
}

func (svc *Service) GetDictType(dictId uint64, tenantType string, tenantId uint64) (*resp.DictTypeResponse, error) {
	fields := (model.DictType{}).GetFields()
	row, err := svc.dao.GetDictTypeById(dictId, tenantType, tenantId, fields)
	if err != nil {
		return nil, err
	}
	return &resp.DictTypeResponse{
		DictId:     row.DictId,
		DictName:   row.DictName,
		DictType:   row.DictType,
		Status:     row.Status,
		CreateBy:   row.CreateBy,
		CreateTime: tools.Time2String(row.CreateTime),
		UpdateBy:   row.UpdateBy,
		UpdateTime: tools.Time2String(row.UpdateTime),
		Remark:     row.Remark,
	}, nil
}

func (svc *Service) CreateDictType(param *req.CreateDictTypeRequest) error {
	if svc.CheckDictNameUniqueForDictType(param.DictName) {
		return errors.New("新增字典类型'" + param.DictName + "'失败，字典名称已存在")
	} else if svc.CheckDictTypeUniqueForDictType(param.DictType) {
		return errors.New("新增字典类型'" + param.DictName + "'失败，字典类型已存在")
	}
	if err := svc.dao.CreateDictType(param); err != nil {
		return err
	}
	return nil
}

func (svc *Service) UpdateDictType(param *req.UpdateDictTypeRequest) error {
	fields := (model.DictType{}).GetFields()
	existByDictType, err := svc.dao.GetDictTypeById(param.DictId, param.TenantType, param.TenantId, fields)
	if err != nil {
		return err
	}
	if existByDictType.DictId == 0 {
		return errors.New("字典类型不存在")
	}
	values := map[string]interface{}{}
	if param.DictName != existByDictType.DictName {
		if svc.CheckDictNameUniqueForDictType(param.DictName) {
			return errors.New("修改字典类型'" + param.DictName + "'失败，字典名称已存在")
		}
		values["dict_name"] = param.DictName
	}
	if param.DictType != existByDictType.DictType {
		if svc.CheckDictTypeUniqueForDictType(param.DictType) {
			return errors.New("修改字典类型'" + param.DictType + "'失败，字典类型已存在")
		}
		values["dict_type"] = param.DictType
	}
	if existByDictType.Status != param.Status {
		values["status"] = param.Status
	}
	if param.Remark != existByDictType.Remark {
		values["remark"] = param.Remark
	}
	values["update_by"] = param.Operater
	values["update_time"] = time.Now()
	if err = svc.dao.UpdateDictType(existByDictType.DictId, values); err != nil {
		return err
	}
	return nil
}

func (svc *Service) CheckDictNameUniqueForDictType(dictName string) bool {
	row, err := svc.dao.GetDictTypeByDictName(dictName, []string{"dict_id"})
	if err != nil {
		return false
	}
	return row.DictId > 0
}

func (svc *Service) CheckDictTypeUniqueForDictType(dictType string) bool {
	row, err := svc.dao.GetDictTypeByDictType(dictType, []string{"dict_id"})
	if err != nil {
		return false
	}
	return row.DictId > 0
}

func (svc *Service) DeleteDictTypes(ids []uint64) error {
	return svc.dao.DeleteDictTypes(ids)
}

// ===========================================================

func (svc *Service) GetDictDataList(param *req.DictDataListRequest, pager *app.Pager) (int64, []*resp.DictDataResponse, error) {
	fields := (model.DictData{}).GetFields()
	pageOption := &app.PageOption{
		Page:      pager.Page,
		PageSize:  pager.PageSize,
		NeedTotal: true,
	}
	rows, total, err := svc.dao.GetDictDataList(param, fields, pageOption)
	if err != nil {
		return 0, nil, err
	}
	res := make([]*resp.DictDataResponse, 0, len(rows))
	for _, row := range rows {
		res = append(res, &resp.DictDataResponse{
			DictCode:   row.DictCode,
			DictSort:   row.DictSort,
			DictLabel:  row.DictLabel,
			DictValue:  row.DictValue,
			DictType:   row.DictType,
			CssClass:   row.CssClass,
			ListClass:  row.ListClass,
			IsDefault:  row.IsDefault,
			Status:     row.Status,
			CreateBy:   row.CreateBy,
			CreateTime: tools.Time2String(row.CreateTime),
			UpdateBy:   row.UpdateBy,
			UpdateTime: tools.Time2String(row.UpdateTime),
			Remark:     row.Remark,
		})
	}
	return total, res, nil
}

func (svc *Service) GetDictData(dictCode uint64, tenantType string, tenantId uint64) (*resp.DictDataResponse, error) {
	fields := (model.DictData{}).GetFields()
	row, err := svc.dao.GetDictDataById(dictCode, tenantType, tenantId, fields)
	if err != nil {
		return nil, err
	}
	return &resp.DictDataResponse{
		DictCode:   row.DictCode,
		DictSort:   row.DictSort,
		DictLabel:  row.DictLabel,
		DictValue:  row.DictValue,
		DictType:   row.DictType,
		CssClass:   row.CssClass,
		ListClass:  row.ListClass,
		IsDefault:  row.IsDefault,
		Status:     row.Status,
		CreateBy:   row.CreateBy,
		CreateTime: tools.Time2String(row.CreateTime),
		UpdateBy:   row.UpdateBy,
		UpdateTime: tools.Time2String(row.UpdateTime),
		Remark:     row.Remark,
	}, nil
}

func (svc *Service) CreateDictData(param *req.CreateDictDataRequest) error {
	if svc.CheckDictLabelUniqueForDictData(param.DictType, param.DictLabel, param.TenantType, param.TenantId) {
		return errors.New("新增字典数据'" + param.DictLabel + "'失败，字典数据名称已存在")
	} else if svc.CheckDictValueUniqueForDictData(param.DictType, param.DictValue, param.TenantType, param.TenantId) {
		return errors.New("新增字典数据'" + param.DictLabel + "'失败，字典数据键值已存在")
	}
	if err := svc.dao.CreateDictData(param); err != nil {
		return err
	}
	return nil
}

func (svc *Service) UpdateDictData(param *req.UpdateDictDataRequest) error {
	fields := (model.DictData{}).GetFields()
	existByDictData, err := svc.dao.GetDictDataById(param.DictCode, param.TenantType, param.TenantId, fields)
	if err != nil {
		return err
	}
	if existByDictData.DictCode == 0 {
		return errors.New("字典数据不存在")
	}
	values := map[string]interface{}{}
	if param.DictLabel != existByDictData.DictLabel {
		if svc.CheckDictLabelUniqueForDictData(param.DictType, param.DictLabel, param.TenantType, param.TenantId) {
			return errors.New("修改字典数据'" + param.DictLabel + "'失败，字典数据名称已存在")
		}
		values["dict_label"] = param.DictLabel
	}
	if param.DictValue != existByDictData.DictValue {
		if svc.CheckDictValueUniqueForDictData(param.DictType, param.DictValue, param.TenantType, param.TenantId) {
			return errors.New("修改字典数据'" + param.DictValue + "'失败，字典数据键值已存在")
		}
		values["dict_value"] = param.DictValue
	}
	if param.DictSort != existByDictData.DictSort {
		values["dict_sort"] = param.DictSort
	}
	if param.CssClass != existByDictData.CssClass {
		values["css_class"] = param.CssClass
	}
	if param.ListClass != existByDictData.ListClass {
		values["list_class"] = param.ListClass
	}
	if existByDictData.Status != param.Status {
		values["status"] = param.Status
	}
	if param.Remark != existByDictData.Remark {
		values["remark"] = param.Remark
	}
	values["update_by"] = param.Operater
	values["update_time"] = time.Now()
	if err = svc.dao.UpdateDictData(existByDictData.DictCode, values); err != nil {
		return err
	}
	return nil
}

func (svc *Service) CheckDictLabelUniqueForDictData(dictType, dictLabel, tenantType string, tenantId uint64) bool {
	row, err := svc.dao.GetDictDataByDictLabel(dictType, dictLabel, tenantType, tenantId, []string{"dict_code"})
	if err != nil {
		return false
	}
	return row.DictCode > 0
}

func (svc *Service) CheckDictValueUniqueForDictData(dictType, dictValue, tenantType string, tenantId uint64) bool {
	row, err := svc.dao.GetDictDataByDictValue(dictType, dictValue, tenantType, tenantId, []string{"dict_code"})
	if err != nil {
		return false
	}
	return row.DictCode > 0
}

func (svc *Service) DeleteDictDatas(ids []uint64) error {
	return svc.dao.DeleteDictDatas(ids)
}

func (svc *Service) GetDictDataByType(dictType, tenantType string, tenantId uint64) ([]*resp.DictDataResponse, error) {
	fields := (model.DictData{}).GetFields()
	rows, err := svc.dao.GetDictDataByType(dictType, tenantType, tenantId, fields)
	if err != nil {
		return nil, err
	}

	res := make([]*resp.DictDataResponse, 0, len(rows))
	for _, row := range rows {
		res = append(res, &resp.DictDataResponse{
			DictCode:   row.DictCode,
			DictSort:   row.DictSort,
			DictLabel:  row.DictLabel,
			DictValue:  row.DictValue,
			DictType:   row.DictType,
			CssClass:   row.CssClass,
			ListClass:  row.ListClass,
			IsDefault:  row.IsDefault,
			Status:     row.Status,
			CreateBy:   row.CreateBy,
			CreateTime: tools.Time2String(row.CreateTime),
			UpdateBy:   row.UpdateBy,
			UpdateTime: tools.Time2String(row.UpdateTime),
			Remark:     row.Remark,
		})
	}
	return res, nil
}
