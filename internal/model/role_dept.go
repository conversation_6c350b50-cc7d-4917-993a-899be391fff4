package model

type RoleDept struct {
	RoleId     uint64 `gorm:"column:role_id;primaryKey;comment:角色ID"`
	DeptId     uint64 `gorm:"column:dept_id;primaryKey;comment:部门ID"`
	TenantType string `gorm:"column:tenant_type;comment:租户类型（platform/partner/store）"`
	TenantId   uint64 `gorm:"column:tenant_id;comment:租户ID（平台ID/合伙人ID/门店ID）"`
}

func (RoleDept) TableName() string {
	return "role_dept"
}

func (RoleDept) GetFields() []string {
	return []string{
		"role_id",
		"dept_id",
		"tenant_type",
		"tenant_id",
	}
}
