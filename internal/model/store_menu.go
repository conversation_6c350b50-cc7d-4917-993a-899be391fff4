package model

import (
	"time"
)

type StoreMenu struct {
	MenuId     uint64    `gorm:"column:menu_id;primaryKey;comment:菜单ID"`
	MenuName   string    `gorm:"column:menu_name;comment:菜单名称"`
	ParentId   uint64    `gorm:"column:parent_id;comment:父菜单ID"`
	OrderNum   int       `gorm:"column:order_num;comment:显示顺序"`
	Path       string    `gorm:"column:path;comment:路由地址"`
	Component  string    `gorm:"column:component;comment:组件路径"`
	Query      string    `gorm:"column:query;comment:路由参数"`
	RouteName  string    `gorm:"column:route_name;comment:路由名称"`
	IsFrame    int       `gorm:"column:is_frame;comment:是否为外链（0是 1否）"`
	IsCache    int       `gorm:"column:is_cache;comment:是否缓存（0缓存 1不缓存）"`
	MenuType   string    `gorm:"column:menu_type;comment:菜单类型（M目录 C菜单 F按钮）"`
	Visible    string    `gorm:"column:visible;comment:菜单状态（0显示 1隐藏）"`
	Status     string    `gorm:"column:status;comment:菜单状态（0正常 1停用）"`
	Perms      string    `gorm:"column:perms;comment:权限标识"`
	Icon       string    `gorm:"column:icon;comment:菜单图标"`
	CreateBy   string    `gorm:"column:create_by;comment:创建者"`
	CreateTime time.Time `gorm:"column:create_time;comment:创建时间"`
	UpdateBy   string    `gorm:"column:update_by;comment:更新者"`
	UpdateTime time.Time `gorm:"column:update_time;comment:更新时间"`
	Remark     string    `gorm:"column:remark;comment:备注"`
}

func (StoreMenu) TableName() string {
	return "store_menu"
}

func (StoreMenu) GetFields() []string {
	return []string{
		"menu_id",
		"menu_name",
		"parent_id",
		"order_num",
		"path",
		"component",
		"query",
		"route_name",
		"is_frame",
		"is_cache",
		"menu_type",
		"visible",
		"status",
		"perms",
		"icon",
		"create_by",
		"create_time",
		"update_by",
		"update_time",
		"remark",
	}
}
