package model

import (
	"time"
)

type DictData struct {
	DictCode   uint64    `gorm:"column:dict_code;primaryKey;comment:字典编码"`
	DictSort   int       `gorm:"column:dict_sort;comment:字典排序"`
	DictLabel  string    `gorm:"column:dict_label;comment:字典标签"`
	DictValue  string    `gorm:"column:dict_value;comment:字典键值"`
	DictType   string    `gorm:"column:dict_type;comment:字典类型"`
	CssClass   string    `gorm:"column:css_class;comment:样式属性（其他样式扩展）"`
	ListClass  string    `gorm:"column:list_class;comment:表格回显样式"`
	IsDefault  string    `gorm:"column:is_default;comment:是否默认（Y是 N否）"`
	Status     string    `gorm:"column:status;comment:状态（0正常 1停用）"`
	CreateBy   string    `gorm:"column:create_by;comment:创建者"`
	CreateTime time.Time `gorm:"column:create_time;comment:创建时间"`
	UpdateBy   string    `gorm:"column:update_by;comment:更新者"`
	UpdateTime time.Time `gorm:"column:update_time;comment:更新时间"`
	Remark     string    `gorm:"column:remark;comment:备注"`
	TenantType string    `gorm:"column:tenant_type;comment:租户类型（platform/partner/store）"`
	TenantId   uint64    `gorm:"column:tenant_id;comment:租户ID（平台ID/合伙人ID/门店ID）"`
}

func (DictData) TableName() string {
	return "dict_data"
}

func (DictData) GetFields() []string {
	return []string{
		"dict_code",
		"dict_sort",
		"dict_label",
		"dict_value",
		"dict_type",
		"css_class",
		"list_class",
		"is_default",
		"status",
		"create_by",
		"create_time",
		"update_by",
		"update_time",
		"remark",
		"tenant_type",
		"tenant_id",
	}
}
