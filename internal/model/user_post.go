package model

type UserPost struct {
	UserId     uint64 `gorm:"column:user_id;primaryKey;comment:用户ID"`
	PostId     uint64 `gorm:"column:post_id;primaryKey;comment:岗位ID"`
	TenantType string `gorm:"column:tenant_type;comment:租户类型（platform/partner/store）"`
	TenantId   uint64 `gorm:"column:tenant_id;comment:租户ID（平台ID/合伙人ID/门店ID）"`
}

func (UserPost) TableName() string {
	return "user_post"
}

func (UserPost) GetFields() []string {
	return []string{
		"user_id",
		"post_id",
		"tenant_type",
		"tenant_id",
	}
}
