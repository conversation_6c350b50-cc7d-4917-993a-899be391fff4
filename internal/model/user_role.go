package model

type UserRole struct {
	UserId     uint64 `gorm:"column:user_id;primaryKey;comment:用户ID"`
	RoleId     uint64 `gorm:"column:role_id;primaryKey;comment:角色ID"`
	TenantType string `gorm:"column:tenant_type;comment:租户类型（platform/partner/store）"`
	TenantId   uint64 `gorm:"column:tenant_id;comment:租户ID（平台ID/合伙人ID/门店ID）"`
}

func (UserRole) TableName() string {
	return "user_role"
}

func (UserRole) GetFields() []string {
	return []string{
		"user_id",
		"role_id",
		"tenant_type",
		"tenant_id",
	}
}
