package model

type RoleMenu struct {
	RoleId     uint64 `gorm:"column:role_id;primaryKey;comment:角色ID"`
	MenuId     uint64 `gorm:"column:menu_id;primaryKey;comment:菜单ID"`
	TenantType string `gorm:"column:tenant_type;comment:租户类型（platform/partner/store）"`
	TenantId   uint64 `gorm:"column:tenant_id;comment:租户ID（平台ID/合伙人ID/门店ID）"`
}

func (RoleMenu) TableName() string {
	return "role_menu"
}

func (RoleMenu) GetFields() []string {
	return []string{
		"role_id",
		"menu_id",
		"tenant_type",
		"tenant_id",
	}
}
