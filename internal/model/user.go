package model

import (
	"time"
)

type User struct {
	UserId uint64 `gorm:"column:user_id;primaryKey;comment:用户ID"`
	DeptId uint64 `gorm:"column:dept_id;comment:部门ID"`
	UserName string `gorm:"column:user_name;comment:用户账号"`
	NickName string `gorm:"column:nick_name;comment:用户昵称"`
	UserType string `gorm:"column:user_type;comment:用户类型（00系统用户）"`
	Email string `gorm:"column:email;comment:用户邮箱"`
	Phonenumber string `gorm:"column:phonenumber;comment:手机号码"`
	Sex string `gorm:"column:sex;comment:用户性别（0男 1女 2未知）"`
	Avatar string `gorm:"column:avatar;comment:头像地址"`
	Password string `gorm:"column:password;comment:密码"`
	Salt string `gorm:"column:salt;comment:加盐"`
	Status string `gorm:"column:status;comment:帐号状态（0正常 1停用）"`
	DelFlag string `gorm:"column:del_flag;comment:删除标志（0代表存在 2代表删除）"`
	LoginIp string `gorm:"column:login_ip;comment:最后登录IP"`
	LoginDate time.Time `gorm:"column:login_date;comment:最后登录时间"`
	CreateBy string `gorm:"column:create_by;comment:创建者"`
	CreateTime time.Time `gorm:"column:create_time;comment:创建时间"`
	UpdateBy string `gorm:"column:update_by;comment:更新者"`
	UpdateTime time.Time `gorm:"column:update_time;comment:更新时间"`
	Remark string `gorm:"column:remark;comment:备注"`
	IsAdmin string `gorm:"column:is_admin;comment:是否是admin（0普通 1管理员）"`
	TenantType string `gorm:"column:tenant_type;comment:租户类型（platform/partner/store）"`
	TenantId uint64 `gorm:"column:tenant_id;comment:租户ID（平台ID/合伙人ID/门店ID）"`
	
}

func (User) TableName() string {
	return "user"
}

func (User) GetFields() []string {
	return []string{
		"user_id",
		"dept_id",
		"user_name",
		"nick_name",
		"user_type",
		"email",
		"phonenumber",
		"sex",
		"avatar",
		"password",
		"salt",
		"status",
		"del_flag",
		"login_ip",
		"login_date",
		"create_by",
		"create_time",
		"update_by",
		"update_time",
		"remark",
		"is_admin",
		"tenant_type",
		"tenant_id",
		
	}
}
