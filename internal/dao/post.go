package dao

import (
	"time"
	"ysa-auth/internal/model"
	"ysa-auth/pkg/app"
	"ysa-auth/pkg/app/req"
)

// CreatePost 创建岗位
func (d *Dao) CreatePost(param *req.CreatePostRequest) error {
	now := time.Now()
	post := &model.Post{
		PostCode:   param.PostCode,
		PostName:   param.PostName,
		PostSort:   param.PostSort,
		Status:     param.Status,
		CreateBy:   param.Operater,
		CreateTime: now,
		UpdateBy:   param.Operater,
		UpdateTime: now,
		Remark:     param.Remark,
		TenantType: param.TenantType,
		TenantId:   param.TenantId,
	}
	return d.engine.Create(post).Error
}

// UpdatePost 更新岗位
func (d *Dao) UpdatePost(postId uint64, values map[string]interface{}) error {
	return d.engine.Model(&model.Post{}).Where("post_id = ?", postId).Updates(values).Error
}

// DeletePost 删除岗位
func (d *Dao) DeletePost(postId uint64) error {
	return d.engine.Delete(&model.Post{}, "post_id = ?", postId).Error
}

// DeletePosts 批量删除岗位
func (d *Dao) DeletePosts(postIds []uint64) error {
	if len(postIds) == 0 {
		return nil
	}
	if len(postIds) == 1 {
		return d.DeletePost(postIds[0])
	}
	return d.engine.Delete(&model.Post{}, "post_id in ?", postIds).Error
}

// GetPostById 根据ID获取岗位
func (d *Dao) GetPostById(postId uint64, tenantType string, tenantId uint64, fields []string) (*model.Post, error) {
	var post model.Post
	query := d.engine.Where("post_id = ? and tenant_type = ? and tenant_id = ? ", postId, tenantType, tenantId)
	if len(fields) > 0 {
		query = query.Select(fields)
	}
	err := query.First(&post).Error
	return &post, err
}

// GetPostByPostIds 根据ID列表获取岗位
func (d *Dao) GetPostByPostIds(postIds []uint64, tenantType string, tenantId uint64, fields []string) ([]*model.Post, error) {
	var posts []*model.Post
	query := d.engine.Where("post_id in ? and tenant_type = ? and tenant_id = ? ", postIds, tenantType, tenantId).Order("post_id asc")
	if len(fields) > 0 {
		query = query.Select(fields)
	}
	err := query.Find(&posts).Error
	return posts, err
}

// GetPostByPostCode 根据岗位编码获取岗位
func (d *Dao) GetPostByPostCode(postCode string, tenantType string, tenantId uint64, fields []string) (*model.Post, error) {
	var post model.Post
	query := d.engine.Where("post_code = ? and tenant_type = ? and tenant_id = ? ", postCode, tenantType, tenantId)
	if len(fields) > 0 {
		query = query.Select(fields)
	}
	err := query.First(&post).Error
	return &post, err
}

// GetPostByPostName 根据岗位名称获取岗位
func (d *Dao) GetPostByPostName(postName string, tenantType string, tenantId uint64, fields []string) (*model.Post, error) {
	var post model.Post
	query := d.engine.Where("post_name = ? and tenant_type = ? and tenant_id = ? ", postName, tenantType, tenantId)
	if len(fields) > 0 {
		query = query.Select(fields)
	}
	err := query.First(&post).Error
	return &post, err
}

// GetPostList 获取岗位列表
func (d *Dao) GetPostList(param *req.PostListRequest, fields []string, pageOption *app.PageOption) ([]*model.Post, int64, error) {
	var posts []*model.Post
	query := d.engine.Model(&model.Post{})
	if param.PostCode != "" {
		query = query.Where("post_code = ?", param.PostCode)
	}
	if param.PostName != "" {
		query = query.Where("post_name like ?", "%"+param.PostName+"%")
	}
	if param.Status != "" {
		query = query.Where("status = ?", param.Status)
	}
	if param.TenantType != "" {
		query = query.Where("tenant_type = ?", param.TenantType)
	}
	if param.TenantId > 0 {
		query = query.Where("tenant_id = ?", param.TenantId)
	}

	// 如果指定了字段，则只查询指定字段
	if len(fields) > 0 {
		query = query.Select(fields)
	}

	// 使用统一的分页方法
	query, total, err := d.ApplyPaginationToQuery(query, pageOption)
	if err != nil {
		return nil, 0, err
	}

	err = query.Find(&posts).Error
	return posts, total, err
}

// GetPostAll 获取所有岗位
func (d *Dao) GetPostAll(fields []string, tenantType string, tenantId uint64) ([]*model.Post, error) {
	var posts []*model.Post
	query := d.engine.Model(&model.Post{})
	if tenantType != "" {
		query = query.Where("tenant_type = ?", tenantType)
	}
	if tenantId > 0 {
		query = query.Where("tenant_id = ?", tenantId)
	}
	// 如果指定了字段，则只查询指定字段
	if len(fields) > 0 {
		query = query.Select(fields)
	}
	err := query.Find(&posts).Error
	return posts, err
}

// GetPostsByUserId 根据用户ID获取岗位列表
func (d *Dao) GetPostsByUserId(userId uint64, fields []string) ([]*model.Post, error) {
	var posts []*model.Post
	query := d.engine.Table("post p").
		Joins("left join user_post up on p.post_id = up.post_id").
		Where("up.user_id = ? AND p.status = ?", userId, "0")

	if len(fields) > 0 {
		query = query.Select(fields)
	}

	err := query.Order("p.post_sort").Find(&posts).Error
	return posts, err
}
