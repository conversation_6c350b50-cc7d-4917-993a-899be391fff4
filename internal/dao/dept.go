package dao

import (
	"ysa-auth/internal/model"
	"ysa-auth/pkg/app/req"
)

// CreateDept 创建部门
func (d *Dao) CreateDept(dept *model.Dept) error {
	return d.engine.Create(dept).Error
}

// UpdateDept 更新部门
func (d *Dao) UpdateDept(dept *model.Dept) error {
	return d.engine.Model(&model.Dept{}).Where("dept_id = ?", dept.DeptId).Updates(dept).Error
}

// GetDeptById 根据ID获取部门
func (d *Dao) GetDeptById(deptId uint64, tenantType string, tenantId uint64, fields []string) (*model.Dept, error) {
	var dept model.Dept
	query := d.engine.Where("dept_id = ? AND del_flag = ? AND tenant_type = ? AND tenant_id = ?", deptId, "0", tenantType, tenantId)
	if len(fields) > 0 {
		query = query.Select(fields)
	}
	err := query.First(&dept).Error
	return &dept, err
}

// GetDeptMapByDeptIds 根据部门ID列表获取部门列表
func (d *Dao) GetDeptMapByDeptIds(deptIds []uint64, fields []string) (map[uint64]*model.Dept, error) {
	var depts []*model.Dept
	query := d.engine.Where("dept_id IN (?) AND del_flag = ?", deptIds, "0")
	if len(fields) > 0 {
		query = query.Select(fields)
	}
	err := query.Find(&depts).Error
	if err != nil {
		return nil, err
	}
	deptMap := make(map[uint64]*model.Dept)
	for _, dept := range depts {
		deptMap[dept.DeptId] = dept
	}
	return deptMap, nil
}

// GetDeptList 获取部门列表
func (d *Dao) GetDeptList(param *req.DeptListRequest, fields []string) ([]*model.Dept, error) {
	var depts []*model.Dept
	query := d.engine.Where("del_flag = ?", "0")

	if param.DeptName != "" {
		query = query.Where("dept_name like ?", "%"+param.DeptName+"%")
	}
	if param.Status != "" {
		query = query.Where("status = ?", param.Status)
	}

	if param.TenantType != "" {
		query = query.Where("tenant_type = ?", param.TenantType)
	}

	if param.TenantId > 0 {
		query = query.Where("tenant_id = ?", param.TenantId)
	}

	if len(fields) > 0 {
		query = query.Select(fields)
	}

	err := query.Order("order_num").Find(&depts).Error
	return depts, err
}

// GetDeptListByParentId 根据父ID获取部门列表
func (d *Dao) GetDeptListByParentId(parentId uint64, fields []string) ([]*model.Dept, error) {
	var depts []*model.Dept
	query := d.engine.Where("parent_id = ? AND del_flag = ?", parentId, "0")

	if len(fields) > 0 {
		query = query.Select(fields)
	}

	err := query.Order("order_num").Find(&depts).Error
	return depts, err
}

// HasChildByDeptId 判断是否有子部门
func (d *Dao) HasChildByDeptId(deptId uint64) (bool, error) {
	var count int64
	err := d.engine.Model(&model.Dept{}).Where("parent_id = ? AND del_flag = ?", deptId, "0").Count(&count).Error
	return count > 0, err
}
