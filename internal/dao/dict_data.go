package dao

import (
	"time"
	"ysa-auth/internal/model"
	"ysa-auth/pkg/app"
	"ysa-auth/pkg/app/req"
)

// CreateDictData 创建字典数据
func (d *Dao) CreateDictData(param *req.CreateDictDataRequest) error {
	now := time.Now()
	dictData := &model.DictData{
		DictSort:   param.DictSort,
		DictLabel:  param.DictLabel,
		DictValue:  param.DictValue,
		DictType:   param.DictType,
		CssClass:   param.CssClass,
		ListClass:  param.ListClass,
		Status:     param.Status,
		CreateBy:   param.Operater,
		CreateTime: now,
		UpdateBy:   param.Operater,
		UpdateTime: now,
		Remark:     param.Remark,
		TenantType: param.TenantType,
		TenantId:   param.TenantId,
	}
	return d.engine.Create(dictData).Error
}

// UpdateDictData 更新字典数据
func (d *Dao) UpdateDictData(id uint64, values map[string]interface{}) error {
	return d.engine.Model(&model.DictData{}).Where("dict_code = ?", id).Updates(values).Error
}

// DeleteDictData 删除字典数据
func (d *Dao) DeleteDictData(dictCode uint64) error {
	return d.engine.Delete(&model.DictData{}, "dict_code = ?", dictCode).Error
}

// DeleteDictDatas 批量删除字典数据
func (d *Dao) DeleteDictDatas(dictCodes []uint64) error {
	if len(dictCodes) == 0 {
		return nil
	}
	if len(dictCodes) == 1 {
		return d.DeleteDictData(dictCodes[0])
	}
	return d.engine.Where("dict_code in (?)", dictCodes).Delete(&model.DictData{}).Error
}

// GetDictDataById 根据ID获取字典数据
func (d *Dao) GetDictDataById(dictCode uint64, tenantType string, tenantId uint64, fields []string) (*model.DictData, error) {
	var dictData model.DictData
	query := d.engine.Where("dict_code = ? and tenant_type = ? and tenant_id = ?", dictCode, tenantType, tenantId)
	if len(fields) > 0 {
		query = query.Select(fields)
	}
	err := query.First(&dictData).Error
	return &dictData, err
}

// GetDictDataByDictLabel 根据字典标签获取字典数据
func (d *Dao) GetDictDataByDictLabel(dictType, dictLabel, tenantType string, tenantId uint64, fields []string) (*model.DictData, error) {
	var dictData model.DictData
	query := d.engine.Where("dict_type = ? and dict_label = ? and tenant_type = ? and tenant_id = ?", dictType, dictLabel, tenantType, tenantId)
	if len(fields) > 0 {
		query = query.Select(fields)
	}
	err := query.First(&dictData).Error
	return &dictData, err
}

// GetDictDataByDictValue 根据字典值获取字典数据
func (d *Dao) GetDictDataByDictValue(dictType, dictValue, tenantType string, tenantId uint64, fields []string) (*model.DictData, error) {
	var dictData model.DictData
	query := d.engine.Where("dict_type = ? and dict_value = ? and tenant_type = ? and tenant_id = ?", dictType, dictValue, tenantType, tenantId)
	if len(fields) > 0 {
		query = query.Select(fields)
	}
	err := query.First(&dictData).Error
	return &dictData, err
}

// GetDictDataList 获取字典数据列表
func (d *Dao) GetDictDataList(param *req.DictDataListRequest, fields []string, pageOption *app.PageOption) ([]*model.DictData, int64, error) {
	var dictDataList []*model.DictData
	query := d.engine.Model(&model.DictData{})
	if param.DictType != "" {
		query = query.Where("dict_type = ?", param.DictType)
	}
	if param.DictLabel != "" {
		query = query.Where("dict_label like ?", "%"+param.DictLabel+"%")
	}
	if param.Status != "" {
		query = query.Where("status = ?", param.Status)
	}
	if param.TenantType != "" {
		query = query.Where("tenant_type = ?", param.TenantType)
	}
	if param.TenantId > 0 {
		query = query.Where("tenant_id = ?", param.TenantId)
	}

	// 如果指定了字段，则只查询指定字段
	if len(fields) > 0 {
		query = query.Select(fields)
	}

	// 使用统一的分页方法
	query, total, err := d.ApplyPaginationToQuery(query, pageOption)
	if err != nil {
		return nil, 0, err
	}

	err = query.Find(&dictDataList).Error
	return dictDataList, total, err
}

// GetDictDataByType 根据字典类型查询字典数据
func (d *Dao) GetDictDataByType(dictType, tenantType string, tenantId uint64, fields []string) ([]*model.DictData, error) {
	var dictDataList []*model.DictData
	query := d.engine.Where("dict_type = ? AND status = ? AND tenant_type = ? AND tenant_id = ?", dictType, "0", tenantType, tenantId)

	if len(fields) > 0 {
		query = query.Select(fields)
	}

	err := query.Order("dict_sort").Find(&dictDataList).Error
	return dictDataList, err
}
