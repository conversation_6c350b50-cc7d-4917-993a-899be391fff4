package dao

import (
	"time"
	"ysa-auth/internal/model"
	"ysa-auth/pkg/app/req"
	"ysa-auth/pkg/tools/convert"
)

// CreateDictType 创建字典类型
func (d *Dao) CreateDictType(param *req.CreateDictTypeRequest) error {
	now := time.Now()
	dictType := &model.DictType{
		DictName:   param.DictName,
		DictType:   param.DictType,
		Status:     param.Status,
		CreateBy:   param.Operater,
		CreateTime: now,
		UpdateBy:   param.Operater,
		UpdateTime: now,
		Remark:     param.Remark,
		TenantType: param.TenantType,
		TenantId:   param.TenantId,
	}
	return d.engine.Create(dictType).Error
}

// UpdateDictType 更新字典类型
func (d *Dao) UpdateDictType(id uint64, values map[string]interface{}) error {
	return d.engine.Model(&model.DictType{}).Where("dict_id = ?", id).Updates(values).Error
}

// DeleteDictType 删除字典类型
func (d *Dao) DeleteDictType(dictId uint64) error {
	return d.engine.Delete(&model.DictType{}, "dict_id = ?", dictId).Error
}

// DeleteDictTypes 批量删除字典类型
func (d *Dao) DeleteDictTypes(dictIds []uint64) error {
	if len(dictIds) == 0 {
		return nil
	}
	if len(dictIds) == 1 {
		return d.DeleteDictType(dictIds[0])
	}
	return d.engine.Where("dict_id in (?)", dictIds).Delete(&model.DictType{}).Error
}

// GetDictTypeById 根据ID获取字典类型
func (d *Dao) GetDictTypeById(dictId uint64, tenantType string, tenantId uint64, fields []string) (*model.DictType, error) {
	var dictType model.DictType
	query := d.engine.Where("dict_id = ? and tenant_type = ? and tenant_id = ?", dictId, tenantType, tenantId)
	if len(fields) > 0 {
		query = query.Select(fields)
	}
	err := query.First(&dictType).Error
	return &dictType, err
}

// GetDictTypeByDictName 根据字典名称获取字典类型
func (d *Dao) GetDictTypeByDictName(dictName string, fields []string) (*model.DictType, error) {
	var dictType model.DictType
	query := d.engine.Where("dict_name = ?", dictName)
	if len(fields) > 0 {
		query = query.Select(fields)
	}
	err := query.First(&dictType).Error
	return &dictType, err
}

// GetDictTypeByDictType 根据字典类型获取字典类型
func (d *Dao) GetDictTypeByDictType(dictType string, fields []string) (*model.DictType, error) {
	var dictTypeModel model.DictType
	query := d.engine.Where("dict_type = ?", dictType)
	if len(fields) > 0 {
		query = query.Select(fields)
	}
	err := query.First(&dictTypeModel).Error
	return &dictTypeModel, err
}

// GetDictTypeList 获取字典类型列表
func (d *Dao) GetDictTypeList(param *req.DictTypeListRequest, fields []string, page, pageSize int) ([]*model.DictType, int64, error) {
	var dictTypes []*model.DictType
	var total int64
	query := d.engine.Model(&model.DictType{})
	if param.DictName != "" {
		query = query.Where("dict_name like ?", "%"+param.DictName+"%")
	}
	if param.DictType != "" {
		query = query.Where("dict_type = ?", param.DictType)
	}
	if param.Status != "" {
		query = query.Where("status = ?", param.Status)
	}
	if param.TenantType != "" {
		query = query.Where("tenant_type =?", param.TenantType)
	}
	if param.TenantId > 0 {
		query = query.Where("tenant_id =?", param.TenantId)
	}
	if param.BeginTime != "" {
		bTime, err := convert.StrTo(param.BeginTime).MustDate()
		if err != nil {
			return nil, 0, err
		}
		query = query.Where("create_time >= ?", bTime)
	}
	if param.EndTime != "" {
		eTime, err := convert.StrTo(param.EndTime).MustDate()
		if err != nil {
			return nil, 0, err
		}
		query = query.Where("create_time <= ?", eTime)
	}
	if err := query.Count(&total).Error; err != nil {
		return nil, 0, err
	}
	// 如果指定了字段，则只查询指定字段
	if len(fields) > 0 {
		query = query.Select(fields)
	}
	// 分页查询
	err := query.Offset((page - 1) * pageSize).Limit(pageSize).Find(&dictTypes).Error
	return dictTypes, total, err
}
