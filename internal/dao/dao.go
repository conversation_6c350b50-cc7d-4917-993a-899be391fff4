package dao

import (
	"time"
	"ysa-auth/pkg/app"
	"ysa-auth/pkg/tools"

	"gorm.io/gorm"
)

type Dao struct {
	engine *gorm.DB
}

func New(engine *gorm.DB) *Dao {
	return &Dao{engine: engine}
}

// HandleLastTimeCondition 处理时间条件，用于查询中添加时间过滤
func (d *Dao) HandleLastTimeCondition(query *gorm.DB, lastTime interface{}, fieldName string) *gorm.DB {
	if lastTime == nil {
		return query
	}

	if fieldName == "" {
		fieldName = "created_at"
	}
	switch t := lastTime.(type) {
	case time.Time:
		if !t.IsZero() {
			// 如果lastTime不为零值，则添加时间过滤条件, 必须转字符串，不然时间会是错误的
			query = query.Where(fieldName+" < ?", tools.Time2String(t))
		}
	case string:
		if t != "" {
			query = query.Where(fieldName+" < ?", t)
		}
	}

	return query
}

// ApplyPaginationToQuery 将分页选项应用到GORM查询对象
// query: 要应用分页的GORM查询对象
// option: 分页选项
// 返回: 应用了分页的查询, 总记录数(如果需要), 错误
func (d *Dao) ApplyPaginationToQuery(query *gorm.DB, option *app.PageOption) (*gorm.DB, int64, error) {
	var total int64 = 0

	// 应用排序
	if option.OrderBy != "" {
		query = query.Order(option.OrderBy)
	}

	// 如果需要获取总数
	if option.NeedTotal {
		if err := query.Count(&total).Error; err != nil {
			return nil, 0, err
		}
	}

	// 应用分页逻辑
	if option.TimeOrder {
		// 基于时间的分页（简单限制数量）
		query = query.Limit(option.PageSize)
	} else if option.UseLastID && option.LastID > 0 {
		// 基于ID的分页（游标分页）
		idField := tools.GetIdFieldFromOrderBy(option.OrderBy)
		operator := tools.GetComparisonOperatorByOrderDirection(option.OrderBy)

		// 应用ID过滤
		query = query.Where(idField+" "+operator+" ?", option.LastID).Limit(option.PageSize)
	} else {
		// 传统分页
		if option.Page <= 0 {
			option.Page = 1
		}
		if option.PageSize <= 0 {
			option.PageSize = 10
		}
		offset := app.GetPageOffset(option.Page, option.PageSize)
		query = query.Offset(offset).Limit(option.PageSize)
	}

	return query, total, nil
}
