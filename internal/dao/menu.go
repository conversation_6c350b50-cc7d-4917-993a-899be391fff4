package dao

import (
	"time"
	"ysa-auth/internal/model"
	"ysa-auth/pkg/app/req"
	"ysa-auth/pkg/tools/convert"
)

// CreateMenu 创建菜单
func (d *Dao) CreateMenu(param *req.CreateMenuRequest) error {
	now := time.Now()
	menu := &model.Menu{
		MenuName:   param.MenuName,
		ParentId:   param.ParentId,
		OrderNum:   param.OrderNum,
		Path:       param.Path,
		Component:  param.Component,
		Query:      param.Query,
		RouteName:  param.RouteName,
		IsFrame:    convert.StrTo(param.IsFrame).MustInt(),
		IsCache:    convert.StrTo(param.IsCache).MustInt(),
		MenuType:   param.MenuType,
		Visible:    param.Visible,
		Status:     param.Status,
		Perms:      param.Perms,
		Icon:       param.Icon,
		CreateBy:   param.Operater,
		CreateTime: now,
		UpdateBy:   param.Operater,
		UpdateTime: now,
		Remark:     param.Remark,
		TenantType: param.TenantType,
		TenantId:   param.TenantId,
	}
	return d.engine.Create(menu).Error
}

// UpdateMenu 更新菜单
func (d *Dao) UpdateMenu(id uint64, tenantType string, tenantId uint64, values map[string]interface{}) error {
	return d.engine.Model(&model.Menu{}).
		Where("menu_id = ? and tenant_type = ? and tenant_id = ?", id, tenantType, tenantId).
		Updates(values).Error
}

// DeleteMenu 删除菜单
func (d *Dao) DeleteMenu(menuId uint64, tenantType string, tenantId uint64) error {
	return d.engine.Delete(&model.Menu{}, "menu_id = ? and tenant_type = ? and tenant_id = ?", menuId, tenantType, tenantId).Error
}

// GetMenuById 根据ID获取菜单
func (d *Dao) GetMenuById(menuId uint64, tenantType string, tenantId uint64, fields []string) (*model.Menu, error) {
	var menu model.Menu
	query := d.engine.Where("menu_id = ? and tenant_type = ? and tenant_id = ?", menuId, tenantType, tenantId)
	if len(fields) > 0 {
		query = query.Select(fields)
	}
	err := query.First(&menu).Error
	return &menu, err
}

// GetMenuByMenuName 根据菜单名称获取菜单
func (d *Dao) GetMenuByMenuName(menuName string, tenantType string, tenantId uint64, fields []string) (*model.Menu, error) {
	var menu model.Menu
	query := d.engine.Where("menu_name = ? and tenant_type = ? and tenant_id = ?", menuName, tenantType, tenantId)
	if len(fields) > 0 {
		query = query.Select(fields)
	}
	err := query.First(&menu).Error
	return &menu, err
}

// GetMenuList 获取菜单列表
func (d *Dao) GetMenuList(param *req.MenuListRequest, fields []string) ([]*model.Menu, error) {
	var menus []*model.Menu
	query := d.engine.Model(&model.Menu{})
	if param.MenuId != 0 {
		query = query.Where("menu_id = ?", param.MenuId)
	}
	if param.ParentId != 0 {
		query = query.Where("parent_id = ?", param.ParentId)
	}
	if param.MenuName != "" {
		query = query.Where("menu_name like ?", "%"+param.MenuName+"%")
	}
	if param.Status != "" {
		query = query.Where("status = ?", param.Status)
	}
	if param.TenantId != 0 {
		query = query.Where("tenant_id = ?", param.TenantId)
	}
	if param.TenantType != "" {
		query = query.Where("tenant_type = ?", param.TenantType)
	}

	if len(fields) > 0 {
		query = query.Select(fields)
	}
	err := query.Order("parent_id, order_num").Find(&menus).Error
	return menus, err
}

// GetMenuListByParentId 根据父ID获取菜单列表
func (d *Dao) GetMenuListByParentId(parentId uint64, tenantType string, tenantId uint64, fields []string) ([]*model.Menu, error) {
	var menus []*model.Menu
	query := d.engine.Where("parent_id = ? AND status = ? AND tenant_id = ? AND tenant_type = ?", parentId, "0", tenantId, tenantType)

	if len(fields) > 0 {
		query = query.Select(fields)
	}

	err := query.Order("order_num").Find(&menus).Error
	return menus, err
}

// GetMenusByRoleId 根据角色ID查询菜单列表
func (d *Dao) GetMenusByRoleId(roleId uint64, fields []string) ([]*model.Menu, error) {
	var menus []*model.Menu
	query := d.engine.Table("menu m").
		Joins("left join role_menu rm on m.menu_id = rm.menu_id").
		Where("rm.role_id = ? AND m.status = ?", roleId, "0")

	if len(fields) > 0 {
		query = query.Select(fields)
	}

	err := query.Order("m.parent_id, m.order_num").Find(&menus).Error
	return menus, err
}

// HasChildByMenuId 判断是否有子菜单
func (d *Dao) HasChildByMenuId(menuId uint64) (bool, error) {
	var count int64
	err := d.engine.Model(&model.Menu{}).Where("parent_id = ? AND status = ?", menuId, "0").Count(&count).Error
	return count > 0, err
}

// GetMenuBasic 获取所有菜单
func (d *Dao) GetMenuBasic(menuType []string, tenantType string, fields []string) ([]*model.Menu, error) {
	var menus []*model.Menu
	query := d.engine.Table("menu_basic").Where("status =? and tenant_type = ?", "0", tenantType)
	if len(menuType) > 0 {
		query = query.Where("menu_type in (?)", menuType)
	}
	if len(fields) > 0 {
		query = query.Select(fields)
	}
	err := query.Order("parent_id, order_num").Find(&menus).Error
	return menus, err
}

// GetMenus 获取所有菜单
func (d *Dao) GetMenus(menuType []string, tenantType string, tenantId uint64, fields []string) ([]*model.Menu, error) {
	var menus []*model.Menu
	query := d.engine.Where("status =? and tenant_type = ? and tenant_id = ?", "0", tenantType, tenantId)
	if len(menuType) > 0 {
		query = query.Where("menu_type in (?)", menuType)
	}
	if len(fields) > 0 {
		query = query.Select(fields)
	}
	err := query.Order("parent_id, order_num").Find(&menus).Error
	return menus, err
}

// GetMenusByUserId 获取用户菜单
func (d *Dao) GetMenusByUserId(userId uint64, menuType []string, tenantType string, tenantId uint64, fields []string) ([]*model.Menu, error) {
	var menus []*model.Menu
	query := d.engine.Table("menu m").
		Joins("left join role_menu rm on m.menu_id = rm.menu_id").
		Joins("left join user_role ur on rm.role_id = ur.role_id").
		Where("ur.user_id =? AND m.status =? AND m.tenant_type = ? AND m.tenant_id = ?", userId, "0", tenantType, tenantId)
	if len(menuType) > 0 {
		query = query.Where("m.menu_type in (?)", menuType)
	}
	if len(fields) > 0 {
		var fields []string
		for _, field := range fields {
			fields = append(fields, "m."+field)
		}
		query = query.Select(fields)
	}
	err := query.Order("m.parent_id, m.order_num").Find(&menus).Error
	return menus, err
}
