package api

import (
	"strings"
	"ysa-auth/internal/service"
	"ysa-auth/pkg/app"
	"ysa-auth/pkg/app/req"
	"ysa-auth/pkg/errcode"
	"ysa-auth/pkg/tools/convert"

	"github.com/gin-gonic/gin"
	"github.com/shrimps80/go-service-utils/middleware"
	"github.com/shrimps80/go-service-utils/utils"
)

type RoleController struct {
}

func NewRoleController() *RoleController {
	return &RoleController{}
}

// 查询角色列表
func (con *RoleController) GetList(c *gin.Context) {
	param := req.RoleListRequest{}
	if err := middleware.ShouldBindWithValidation(c, &param); err != nil {
		utils.Error(c, utils.ErrInvalidParam)
		return
	}
	param.TenantType = c.GetString("tenant_type")
	param.TenantId = c.GetUint64("tenant_id")
	svc := service.New(c.Request.Context())
	pager := app.Pager{Page: app.GetPage(c), PageSize: app.GetPageSize(c)}
	total, roles, err := svc.GetRoleList(&param, &pager)
	if err != nil {
		utils.Error(c, errcode.ErrorGetRoleListFail, utils.WithMessage(err.Error()))
		return
	}
	utils.Success(c, app.PagerResp{
		Page:     pager.Page,
		PageSize: pager.PageSize,
		Total:    total,
		Rows:     roles,
	})
}

// 查询角色详细
func (con *RoleController) GetDetailByRoleId(c *gin.Context) {
	roleId := convert.StrTo(c.Param("role_id")).MustUInt64()
	if roleId <= 0 {
		utils.Error(c, errcode.InvalidParams)
		return
	}
	svc := service.New(c.Request.Context())
	tenantType := c.GetString("tenant_type")
	tenantId := c.GetUint64("tenant_id")
	role, err := svc.GetRole(roleId, tenantType, tenantId)
	if err != nil {
		utils.Error(c, errcode.ErrorGetRoleFail, utils.WithMessage(err.Error()))
		return
	}

	utils.Success(c, role)
}

// 新增角色
func (con *RoleController) CreateRole(c *gin.Context) {
	param := req.CreateRoleRequest{}
	if err := middleware.ShouldBindWithValidation(c, &param); err != nil {
		utils.Error(c, utils.ErrInvalidParam, utils.WithMessage(err.Error()))
		return
	}
	param.Operater = c.GetString("username")
	param.TenantType = c.GetString("tenant_type")
	param.TenantId = c.GetUint64("tenant_id")
	svc := service.New(c.Request.Context())
	err := svc.CreateRole(&param)
	if err != nil {
		utils.Error(c, errcode.ErrorCreateRoleFail, utils.WithMessage(err.Error()))
		return
	}

	utils.Success(c, nil)
}

// 修改角色
func (con *RoleController) UpdateRole(c *gin.Context) {
	param := req.UpdateRoleRequest{}
	if err := middleware.ShouldBindWithValidation(c, &param); err != nil {
		utils.Error(c, utils.ErrInvalidParam)
		return
	}
	param.Operater = c.GetString("username")
	param.TenantType = c.GetString("tenant_type")
	param.TenantId = c.GetUint64("tenant_id")
	svc := service.New(c.Request.Context())
	err := svc.UpdateRole(&param)
	if err != nil {
		utils.Error(c, errcode.ErrorUpdateRoleFail, utils.WithMessage(err.Error()))
		return
	}

	utils.Success(c, nil)
}

// 角色数据权限
func (con *RoleController) UpdateDataScope(c *gin.Context) {
	param := req.AuthDataScopeRequest{}
	if err := middleware.ShouldBindWithValidation(c, &param); err != nil {
		utils.Error(c, utils.ErrInvalidParam)
		return
	}
	param.TenantType = c.GetString("tenant_type")
	param.TenantId = c.GetUint64("tenant_id")
	svc := service.New(c.Request.Context())
	err := svc.InsertAuthDataScope(&param)
	if err != nil {
		utils.Error(c, errcode.ErrorInsertAuthDataScopeFail, utils.WithMessage(err.Error()))
		return
	}

	utils.Success(c, nil)
}

// 角色状态修改
func (con *RoleController) ChangeStatus(c *gin.Context) {
	param := req.ChangeRoleStatusRequest{}

	if err := middleware.ShouldBindWithValidation(c, &param); err != nil {
		utils.Error(c, utils.ErrInvalidParam)
		return
	}
	param.Operater = c.GetString("username")
	param.TenantType = c.GetString("tenant_type")
	param.TenantId = c.GetUint64("tenant_id")
	svc := service.New(c.Request.Context())
	err := svc.UpdateRoleStatus(&param)
	if err != nil {
		utils.Error(c, errcode.ErrorUpdateRoleFail, utils.WithMessage(err.Error()))
		return
	}

	utils.Success(c, nil)
}

// 删除角色
func (con *RoleController) DeleteRole(c *gin.Context) {
	rid := convert.StrTo(c.Param("role_id"))
	var rids []uint64
	if strings.Contains(rid.String(), ",") {
		ids := strings.Split(rid.String(), ",")
		for _, v := range ids {
			userId := convert.StrTo(v).MustUInt64()
			if userId > 0 {
				rids = append(rids, userId)
			}
		}
	} else {
		rids = append(rids, rid.MustUInt64())
	}

	if len(rids) <= 0 {
		utils.Error(c, errcode.InvalidParams)
		return
	}
	tenantType := c.GetString("tenant_type")
	tenantId := c.GetUint64("tenant_id")
	operater := c.GetString("username")
	svc := service.New(c.Request.Context())
	err := svc.DeleteRoles(rids, tenantType, tenantId, operater)
	if err != nil {
		utils.Error(c, errcode.ErrorDeleteRoleFail, utils.WithMessage(err.Error()))
		return
	}

	utils.Success(c, nil)
}

// 查询角色已授权用户列表
func (con *RoleController) GetAllocatedList(c *gin.Context) {
	param := req.SelectRoleUserRequest{}
	if err := middleware.ShouldBindWithValidation(c, &param); err != nil {
		utils.Error(c, utils.ErrInvalidParam)
		return
	}
	param.TenantType = c.GetString("tenant_type")
	param.TenantId = c.GetUint64("tenant_id")
	svc := service.New(c.Request.Context())
	pager := app.Pager{Page: app.GetPage(c), PageSize: app.GetPageSize(c)}
	total, users, err := svc.SelectAllocatedList(&param, &pager)
	if err != nil {
		utils.Error(c, errcode.ErrorGetUserListFail, utils.WithMessage(err.Error()))
		return
	}
	utils.Success(c, app.PagerResp{
		Page:     pager.Page,
		PageSize: pager.PageSize,
		Total:    total,
		Rows:     users,
	})
}

// 查询角色未授权用户列表
func (con *RoleController) GetUnallocatedList(c *gin.Context) {
	param := req.SelectRoleUserRequest{}
	if err := middleware.ShouldBindWithValidation(c, &param); err != nil {
		utils.Error(c, utils.ErrInvalidParam)
		return
	}
	param.TenantType = c.GetString("tenant_type")
	param.TenantId = c.GetUint64("tenant_id")
	svc := service.New(c.Request.Context())
	pager := app.Pager{Page: app.GetPage(c), PageSize: app.GetPageSize(c)}
	total, users, err := svc.SelectUnallocatedList(&param, &pager)
	if err != nil {
		utils.Error(c, errcode.ErrorGetUserListFail, utils.WithMessage(err.Error()))
		return
	}
	utils.Success(c, app.PagerResp{
		Page:     pager.Page,
		PageSize: pager.PageSize,
		Total:    total,
		Rows:     users,
	})
}

// 取消用户授权角色
func (con *RoleController) CancelAuthUser(c *gin.Context) {
	param := req.DeleteAuthUserRequest{}
	if err := middleware.ShouldBindWithValidation(c, &param); err != nil {
		utils.Error(c, utils.ErrInvalidParam)
		return
	}
	param.TenantType = c.GetString("tenant_type")
	param.TenantId = c.GetUint64("tenant_id")
	svc := service.New(c.Request.Context())
	err := svc.DeleteAuthUsers(param.RoleId, param.TenantType, param.TenantId, []uint64{param.UserId})
	if err != nil {
		utils.Error(c, errcode.ErrorDeleteAuthUserFail, utils.WithMessage(err.Error()))
		return
	}
	utils.Success(c, nil)
}

// 批量取消用户授权角色
func (con *RoleController) CancelAllAuthUser(c *gin.Context) {
	param := req.AuthUsersRequest{}

	if err := middleware.ShouldBindWithValidation(c, &param); err != nil {
		utils.Error(c, utils.ErrInvalidParam)
		return
	}
	param.TenantType = c.GetString("tenant_type")
	param.TenantId = c.GetUint64("tenant_id")
	userIdList := strings.Split(param.UserIds, ",")
	var userIds []uint64
	for _, v := range userIdList {
		userId := convert.StrTo(v).MustUInt64()
		userIds = append(userIds, userId)
	}
	svc := service.New(c.Request.Context())
	err := svc.DeleteAuthUsers(param.RoleId, param.TenantType, param.TenantId, userIds)
	if err != nil {
		utils.Error(c, errcode.ErrorDeleteAuthUserFail, utils.WithMessage(err.Error()))
		return
	}
	utils.Success(c, nil)
}

// 授权用户选择
func (con *RoleController) SelectAllAuthUser(c *gin.Context) {
	param := req.AuthUsersRequest{}
	if err := middleware.ShouldBindWithValidation(c, &param); err != nil {
		utils.Error(c, utils.ErrInvalidParam)
		return
	}
	param.TenantType = c.GetString("tenant_type")
	param.TenantId = c.GetUint64("tenant_id")
	svc := service.New(c.Request.Context())
	err := svc.InsertAuthUsers(&param)
	if err != nil {
		utils.Error(c, errcode.ErrorInsertAuthUsersFail, utils.WithMessage(err.Error()))
		return
	}
	utils.Success(c, nil)
}

// 根据角色ID查询部门树结构
func (con *RoleController) GetDeptTreeByRoleId(c *gin.Context) {
	roleId := convert.StrTo(c.Param("role_id")).MustUInt64()

	if roleId <= 0 {
		utils.Error(c, errcode.InvalidParams)
		return
	}
	param := req.DeptListRequest{
		TenantType: c.GetString("tenant_type"),
		TenantId:   c.GetUint64("tenant_id"),
	}
	svc := service.New(c.Request.Context())
	depts, err := svc.GetDeptList(&param)
	tree := svc.BuildDeptTree(depts)
	if err != nil {
		utils.Error(c, errcode.ErrorDeptTreeFail, utils.WithMessage(err.Error()))
		return
	}

	checkIds := svc.GetRoleDeptIds(roleId)
	utils.Success(c, map[string]interface{}{
		"depts":       tree,
		"checkedKeys": checkIds,
	})
}
