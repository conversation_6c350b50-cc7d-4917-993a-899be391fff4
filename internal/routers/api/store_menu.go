package api

import (
	"ysa-auth/internal/service"
	"ysa-auth/pkg/app/req"
	"ysa-auth/pkg/errcode"
	"ysa-auth/pkg/tools/convert"

	"github.com/gin-gonic/gin"
	"github.com/shrimps80/go-service-utils/middleware"
	"github.com/shrimps80/go-service-utils/utils"
)

type StoreMenuController struct {
}

func NewStoreMenuController() *StoreMenuController {
	return &StoreMenuController{}
}

func (con *StoreMenuController) GetList(c *gin.Context) {
	param := req.StoreMenuListRequest{}
	if err := middleware.ShouldBindWithValidation(c, &param); err != nil {
		utils.Error(c, utils.ErrInvalidParam)
		return
	}
	svc := service.New(c.Request.Context())
	menus, err := svc.GetStoreMenuList(&param)
	if err != nil {
		utils.Error(c, errcode.ErrorGetMenuListFail, utils.WithMessage(err.Error()))
		return
	}
	utils.Success(c, menus)
	return
}

func (con *StoreMenuController) GetDetailByMenuId(c *gin.Context) {
	menuId := convert.StrTo(c.Param("menu_id")).MustUInt64()
	if menuId <= 0 {
		utils.Error(c, errcode.InvalidParams)
		return
	}
	svc := service.New(c.Request.Context())
	menu, err := svc.GetStoreMenuByMenuId(menuId)
	if err != nil {
		utils.Error(c, errcode.ErrorGetMenuFail, utils.WithMessage(err.Error()))
		return
	}
	utils.Success(c, menu)
	return
}

func (con *StoreMenuController) GetTreeSelect(c *gin.Context) {
	param := req.StoreMenuListRequest{}
	if err := middleware.ShouldBindWithValidation(c, &param); err != nil {
		utils.Error(c, utils.ErrInvalidParam)
		return
	}
	svc := service.New(c.Request.Context())
	menus, err := svc.GetStoreMenuList(&param)
	tree := svc.BuildStoreMenuTree(menus)
	if err != nil {
		utils.Error(c, errcode.ErrorGetMenuFail, utils.WithMessage(err.Error()))
		return
	}

	utils.Success(c, tree)
	return
}

func (con *StoreMenuController) GetRoleMenuTreeSelect(c *gin.Context) {
	roleId := convert.StrTo(c.Param("role_id")).MustUInt64()
	param := req.StoreMenuListRequest{}
	if err := middleware.ShouldBindWithValidation(c, &param); err != nil {
		utils.Error(c, utils.ErrInvalidParam)
		return
	}
	svc := service.New(c.Request.Context())
	menus, err := svc.GetStoreMenuList(&param)
	tree := svc.BuildStoreMenuTree(menus)
	if err != nil {
		utils.Error(c, errcode.ErrorGetMenuListFail, utils.WithMessage(err.Error()))
		return
	}
	checkIds := svc.GetMenuIdsByRoleId(roleId)
	utils.Success(c, map[string]interface{}{
		"menus":       tree,
		"checkedKeys": checkIds,
	})
	return
}

func (con *StoreMenuController) CreateMenu(c *gin.Context) {
	param := req.CreateStoreMenuRequest{}
	if err := middleware.ShouldBindWithValidation(c, &param); err != nil {
		utils.Error(c, utils.ErrInvalidParam)
		return
	}
	param.Operater = c.GetString("username")
	svc := service.New(c.Request.Context())
	err := svc.CreateStoreMenu(&param)
	if err != nil {
		utils.Error(c, errcode.ErrorCreateMenuFail, utils.WithMessage(err.Error()))
		return
	}

	utils.Success(c, nil)
}

func (con *StoreMenuController) UpdateMenu(c *gin.Context) {
	param := req.UpdateStoreMenuRequest{}
	if err := middleware.ShouldBindWithValidation(c, &param); err != nil {
		utils.Error(c, utils.ErrInvalidParam)
		return
	}
	param.Operater = c.GetString("username")
	svc := service.New(c.Request.Context())
	err := svc.UpdateStoreMenu(&param)
	if err != nil {
		utils.Error(c, errcode.ErrorUpdateMenuFail, utils.WithMessage(err.Error()))
		return
	}
	utils.Success(c, nil)
}

func (con *StoreMenuController) DeleteMenu(c *gin.Context) {
	menuId := convert.StrTo(c.Param("menu_id")).MustUInt64()
	if menuId <= 0 {
		utils.Error(c, errcode.InvalidParams)
		return
	}
	svc := service.New(c.Request.Context())
	err := svc.DeleteStoreMenu(menuId)
	if err != nil {
		utils.Error(c, errcode.ErrorDeleteMenuFail, utils.WithMessage(err.Error()))
		return
	}
	utils.Success(c, nil)
}
