package api

import (
	"strings"
	"ysa-auth/internal/service"
	"ysa-auth/pkg/app"
	"ysa-auth/pkg/app/req"
	"ysa-auth/pkg/errcode"
	"ysa-auth/pkg/tools/convert"

	"github.com/gin-gonic/gin"
	"github.com/shrimps80/go-service-utils/middleware"
	"github.com/shrimps80/go-service-utils/utils"
)

type PostController struct {
}

func NewPostController() *PostController {
	return &PostController{}
}

func (con *PostController) GetList(c *gin.Context) {
	param := req.PostListRequest{}
	if err := middleware.ShouldBindWithValidation(c, &param); err != nil {
		utils.Error(c, utils.ErrInvalidParam)
		return
	}
	param.TenantType = c.GetString("tenant_type")
	param.TenantId = c.GetUint64("tenant_id")
	svc := service.New(c.Request.Context())
	pager := app.Pager{Page: app.GetPage(c), PageSize: app.GetPageSize(c)}
	total, posts, err := svc.GetPostList(&param, &pager)
	if err != nil {
		utils.Error(c, errcode.ErrorGetPostListFail, utils.WithMessage(err.Error()))
		return
	}
	utils.Success(c, app.PagerResp{
		Page:     pager.Page,
		PageSize: pager.PageSize,
		Total:    total,
		Rows:     posts,
	})
}

func (con *PostController) GetDetailByPostId(c *gin.Context) {
	postId := convert.StrTo(c.Param("post_id")).MustUInt64()
	if postId <= 0 {
		utils.Error(c, errcode.InvalidParams)
		return
	}
	svc := service.New(c.Request.Context())
	tenantType := c.GetString("tenant_type")
	tenantId := c.GetUint64("tenant_id")
	post, err := svc.GetPost(postId, tenantType, tenantId)
	if err != nil {
		utils.Error(c, errcode.ErrorGetPostFail, utils.WithMessage(err.Error()))
		return
	}

	utils.Success(c, post)
}

func (con *PostController) CreatePost(c *gin.Context) {
	param := req.CreatePostRequest{}
	if err := middleware.ShouldBindWithValidation(c, &param); err != nil {
		utils.Error(c, utils.ErrInvalidParam)
		return
	}
	param.Operater = c.GetString("username")
	param.TenantType = c.GetString("tenant_type")
	param.TenantId = c.GetUint64("tenant_id")
	svc := service.New(c.Request.Context())
	err := svc.CreatePost(&param)
	if err != nil {
		utils.Error(c, errcode.ErrorCreatePostFail, utils.WithMessage(err.Error()))
		return
	}

	utils.Success(c, nil)
}

func (con *PostController) UpdatePost(c *gin.Context) {
	param := req.UpdatePostRequest{}
	if err := middleware.ShouldBindWithValidation(c, &param); err != nil {
		utils.Error(c, utils.ErrInvalidParam)
		return
	}

	param.Operater = c.GetString("username")
	param.TenantType = c.GetString("tenant_type")
	param.TenantId = c.GetUint64("tenant_id")
	svc := service.New(c.Request.Context())
	err := svc.UpdatePost(&param)
	if err != nil {
		utils.Error(c, errcode.ErrorUpdatePostFail, utils.WithMessage(err.Error()))
		return
	}

	utils.Success(c, nil)
}

func (con *PostController) DeletePost(c *gin.Context) {
	pid := convert.StrTo(c.Param("post_id"))
	var pids []uint64
	// 判断是否有逗号分隔
	if strings.Contains(pid.String(), ",") {
		ids := strings.Split(pid.String(), ",")
		for _, v := range ids {
			postId := convert.StrTo(v).MustUInt64()
			if postId > 0 {
				pids = append(pids, postId)
			}
		}
	} else {
		pids = append(pids, pid.MustUInt64())
	}
	if len(pids) <= 0 {
		utils.Error(c, errcode.InvalidParams)
		return
	}
	svc := service.New(c.Request.Context())
	err := svc.DeletePosts(pids)
	if err != nil {
		utils.Error(c, errcode.ErrorDeletePostFail, utils.WithMessage(err.Error()))
		return
	}

	utils.Success(c, nil)
}
