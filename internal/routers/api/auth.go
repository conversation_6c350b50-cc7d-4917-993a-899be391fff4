package api

import (
	"encoding/json"
	"time"
	"ysa-auth/global"
	"ysa-auth/internal/service"
	"ysa-auth/pkg/app"
	"ysa-auth/pkg/app/req"
	"ysa-auth/pkg/app/resp"
	"ysa-auth/pkg/errcode"
	"ysa-auth/pkg/tools/convert"

	"github.com/gin-gonic/gin"
	"github.com/shrimps80/go-service-utils/middleware"
	"github.com/shrimps80/go-service-utils/utils"
)

type AuthController struct {
}

func NewAuthController() *AuthController {
	return &AuthController{}
}

// Login 用户登录
func (con *AuthController) Login(c *gin.Context) {
	param := req.LoginRequest{}
	if err := middleware.ShouldBindWithValidation(c, &param); err != nil {
		utils.Error(c, utils.ErrInvalidParam, utils.WithMessage(err.Error()))
		return
	}
	svc := service.New(c.Request.Context())
	user, err := svc.CheckUserNamePassword(param.UserName, param.Password)
	if err != nil {
		utils.Error(c, errcode.UnauthorizedAuthNotExist, utils.WithMessage(err.Error()))
		return
	}
	if user.TenantType != param.TenantType {
		if param.TenantId > 0 && param.TenantId != user.TenantId {
			utils.Error(c, errcode.NotFound, utils.WithMessage("登录类型错误"))
			return
		}
	}
	roles := []string{}
	permissions := []string{}
	if user.IsAdmin == "1" {
		roles = append(roles, "admin")
		permissions = append(permissions, "*:*:*")
	} else {
		roles, permissions, err = svc.GetPermissionsByUserId(user.UserId)
		if err != nil {
			utils.Error(c, errcode.UnauthorizedTokenGenerate, utils.WithMessage(err.Error()))
			return
		}
	}
	token, err := app.GenerateToken(user.UserId, user.UserName, user.IsAdmin, user.TenantType, user.TenantId)
	if err != nil {
		utils.Error(c, errcode.UnauthorizedTokenGenerate)
		return
	}
	// 缓存
	expireInt64 := convert.StrTo(global.App.Config.GetString("JWT.Expire")).MustInt64()
	expire := time.Duration(expireInt64) * time.Minute
	global.App.Redis.Set(c, app.UserTokenKey(user.UserId), token, expire)
	rJson, _ := json.Marshal(roles)
	global.App.Redis.Set(c, app.UserRolesKey(user.UserId), rJson, expire)
	pJson, _ := json.Marshal(permissions)
	global.App.Redis.Set(c, app.UserPermissionsKey(user.UserId), pJson, expire)
	// 缓存End

	utils.Success(c, resp.LoginResponse{
		Token:  token,
		Expire: expireInt64,
	})
}

func (con *AuthController) GetInfo(c *gin.Context) {
	userId := c.GetUint64("user_id")
	tenantType := c.GetString("tenant_type")
	tenantId := c.GetUint64("tenant_id")
	svc := service.New(c.Request.Context())
	user, err := svc.GetUser(userId, tenantType, tenantId)
	if err != nil {
		utils.Error(c, errcode.ErrorGetUserFail, utils.WithMessage(err.Error()))
		return
	}
	roles := []string{}
	perms := []string{}
	if user.Admin {
		roles = append(roles, "admin")
		perms = append(perms, "*:*:*")
	} else {
		roleJson, err := global.App.Redis.Get(c, app.UserRolesKey(userId))
		if err != nil {
			utils.Error(c, errcode.ErrorGetUserFail, utils.WithMessage(err.Error()))
			return
		}
		if roleJson != "" {
			_ = json.Unmarshal([]byte(roleJson), &roles)
		}
		permissionJson, err := global.App.Redis.Get(c, app.UserPermissionsKey(userId))
		if err != nil {
			utils.Error(c, errcode.ErrorGetUserFail, utils.WithMessage(err.Error()))
			return
		}
		if permissionJson != "" {
			_ = json.Unmarshal([]byte(permissionJson), &perms)
		}
	}
	utils.Success(c, map[string]interface{}{
		"permissions": perms,
		"roles":       roles,
		"user":        user,
	})
}

func (con *AuthController) GetRouters(c *gin.Context) {
	userId := c.GetUint64("user_id")
	svc := service.New(c.Request.Context())
	routers, err := svc.GetRouters(userId)
	if err != nil {
		utils.Error(c, errcode.ErrorGetRoutersFail, utils.WithMessage(err.Error()))
		return
	}
	utils.Success(c, routers)
}

func (con *AuthController) Logout(c *gin.Context) {
	var (
		token string
	)
	if s := c.GetHeader("Authorization"); s != "" {
		if len(s) > 7 && s[:7] == "Bearer " {
			token = s[7:]
		}
	}
	if token != "" {
		auth, _ := app.ParseToken(token)
		if auth != nil {
			global.App.Redis.Set(c, app.UserTokenKey(auth.UserId), "", 1*time.Second)
			global.App.Redis.Set(c, app.UserRolesKey(auth.UserId), "", 1*time.Second)
			global.App.Redis.Set(c, app.UserPermissionsKey(auth.UserId), "", 1*time.Second)
		}
	}
	utils.Success(c, nil)
}

func (con *AuthController) GetProfileByLoginUser(c *gin.Context) {
	userId := c.GetUint64("user_id")
	tenantType := c.GetString("tenant_type")
	tenantId := c.GetUint64("tenant_id")
	svc := service.New(c.Request.Context())
	user, err := svc.GetUser(userId, tenantType, tenantId)
	if err != nil {
		utils.Error(c, errcode.ErrorGetUserFail, utils.WithMessage(err.Error()))
		return
	}
	var postName []string
	if len(user.Posts) > 0 {
		for i := range user.Posts {
			postName = append(postName, user.Posts[i].PostName)
		}
	}

	var roleName []string
	for i := range user.Roles {
		roleName = append(roleName, user.Roles[i].RoleName)
	}
	utils.Success(c, map[string]interface{}{
		"data":      user,
		"postGroup": postName,
		"roleGroup": roleName,
	})
}

func (con *AuthController) UpdateProfileByLoginUser(c *gin.Context) {
	param := req.UpdateUserProfileRequest{}
	if err := middleware.ShouldBindWithValidation(c, &param); err != nil {
		utils.Error(c, utils.ErrInvalidParam, utils.WithMessage(err.Error()))
		return
	}
	param.Operater = c.GetString("username")
	param.TenantType = c.GetString("tenant_type")
	param.TenantId = c.GetUint64("tenant_id")
	param.UserId = c.GetUint64("user_id")
	svc := service.New(c.Request.Context())
	err := svc.UpdateUserProfile(&param)
	if err != nil {
		utils.Error(c, errcode.ErrorUpdateUserFail, utils.WithMessage(err.Error()))
		return
	}
	utils.Success(c, nil)
}

func (con *AuthController) UpdatePasswordByLoginUser(c *gin.Context) {
	param := req.ChangeNewPwdRequest{}
	if err := middleware.ShouldBindWithValidation(c, &param); err != nil {
		utils.Error(c, utils.ErrInvalidParam, utils.WithMessage(err.Error()))
		return
	}
	param.Operater = c.GetString("username")
	param.TenantType = c.GetString("tenant_type")
	param.TenantId = c.GetUint64("tenant_id")
	param.UserId = c.GetUint64("user_id")
	svc := service.New(c.Request.Context())
	if _, err := svc.CheckUserIdPassword(param.UserId, param.OldPassword); err != nil {
		utils.Error(c, errcode.ErrorUpdateUserFail, utils.WithMessage(err.Error()))
		return
	}
	changeParam := req.ResetPwdRequest{
		UserId:     param.UserId,
		Password:   param.NewPassword,
		Operater:   param.Operater,
		TenantType: param.TenantType,
		TenantId:   param.TenantId,
	}
	if err := svc.ResetPassword(&changeParam); err != nil {
		utils.Error(c, errcode.ErrorUpdateUserFail, utils.WithMessage(err.Error()))
		return
	}
	utils.Success(c, nil)
}
func (con *AuthController) UploadAvatarByLoginUser(c *gin.Context) {
	param := req.ChangeAvatarRequest{}
	if err := middleware.ShouldBindWithValidation(c, &param); err != nil {
		utils.Error(c, utils.ErrInvalidParam, utils.WithMessage(err.Error()))
		return
	}
	param.Operater = c.GetString("username")
	param.TenantType = c.GetString("tenant_type")
	param.TenantId = c.GetUint64("tenant_id")
	param.UserId = c.GetUint64("user_id")
	svc := service.New(c.Request.Context())
	if err := svc.ChangeAvatar(&param); err != nil {
		utils.Error(c, errcode.ErrorUpdateUserFail, utils.WithMessage(err.Error()))
		return
	}
	utils.Success(c, nil)
}

func (con *AuthController) Register(c *gin.Context) {
	tenantType := c.GetString("tenant_type")
	if tenantType != "platform" {
		utils.Error(c, errcode.ErrorNotPlatformTenant, utils.WithMessage("only platform tenant can register"))
		return
	}
	param := req.RegisterRequest{}
	if err := middleware.ShouldBindWithValidation(c, &param); err != nil {
		utils.Error(c, utils.ErrInvalidParam, utils.WithMessage(err.Error()))
		return
	}
	param.Operater = c.GetString("username")
	svc := service.New(c.Request.Context())
	err := svc.CreateAdminUser(&param)
	if err != nil {
		utils.Error(c, errcode.ErrorCreateUserFail, utils.WithMessage(err.Error()))
		return
	}
	utils.Success(c, nil)
}
