package api

import (
	"ysa-auth/pkg/app/req"

	"github.com/gin-gonic/gin"
	"github.com/shrimps80/go-service-utils/middleware"
	"github.com/shrimps80/go-service-utils/utils"
)

type AuthController struct {
}

func NewAuthController() *AuthController {
	return &AuthController{}
}

// Login 用户登录
func (con *AuthController) Login(c *gin.Context) {
	param := req.LoginRequest{}
	if err := middleware.ShouldBindWithValidation(c, &param); err != nil {
		utils.Error(c, utils.ErrInvalidParam, utils.WithMessage(err.Error()))
		return
	}
	// svc := service.New(c.Request.Context())
	// user, err := svc.CheckUserNamePassword(param.UserName, param.Password)
	// if err != nil {
	// 	utils.Error(c, errcode.UnauthorizedAuthNotExist, utils.WithMessage(err.Error()))
	// 	return
	// }
	// if user.TenantType != param.TenantType {
	// 	if param.TenantId > 0 && param.TenantId != user.TenantId {
	// 		utils.Error(c, errcode.NotFound, utils.WithMessage("登录类型错误"))
	// 		return
	// 	}
	// }
	// roles := []string{}
	// permissions := []string{}
	// if user.IsAdmin == "1" {
	// 	roles = append(roles, "admin")
	// 	permissions = append(permissions, "*:*:*")
	// } else {
	// 	roles, permissions, err = svc.GetPermissionsByUserId(user.UserId)
	// 	if err != nil {
	// 		utils.Error(c, errcode.UnauthorizedTokenGenerate, utils.WithMessage(err.Error()))
	// 		return
	// 	}
	// }
	// token, err := app.GenerateToken(user.UserId, user.UserName, user.IsAdmin, user.TenantType, user.TenantId)
	// if err != nil {
	// 	utils.Error(c, errcode.UnauthorizedTokenGenerate)
	// 	return
	// }
	// // 缓存
	// expireInt64 := convert.StrTo(global.App.Config.GetString("JWT.Expire")).MustInt64()
	// expire := time.Duration(expireInt64) * time.Minute
	// global.App.Redis.Set(c, app.UserTokenKey(user.UserId), token, expire)
	// rJson, _ := json.Marshal(roles)
	// global.App.Redis.Set(c, app.UserRolesKey(user.UserId), rJson, expire)
	// pJson, _ := json.Marshal(permissions)
	// global.App.Redis.Set(c, app.UserPermissionsKey(user.UserId), pJson, expire)
	// // 缓存End

	// utils.Success(c, resp.LoginResponse{
	// 	Token:  token,
	// 	Expire: expireInt64,
	// })
}
