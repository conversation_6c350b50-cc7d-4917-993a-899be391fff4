package middleware

import (
	"time"
	"ysa-auth/global"
	"ysa-auth/pkg/app"
	"ysa-auth/pkg/errcode"

	"github.com/gin-gonic/gin"
	"github.com/shrimps80/go-service-utils/utils"
)

func JWT() gin.HandlerFunc {
	return func(c *gin.Context) {
		var (
			token string
		)
		if s := c.<PERSON>("Authorization"); s != "" {
			if len(s) > 7 && s[:7] == "Bearer " {
				token = s[7:]
			}
		}

		if token == "" {
			utils.Error(c, errcode.UnauthorizedTokenError)
			c.Abort()
			return
		}

		claims, err := app.ParseToken(token)
		if err != nil {
			utils.Error(c, errcode.UnauthorizedTokenError)
			c.Abort()
			return
		} else if time.Now().Unix() > claims.ExpiresAt {
			utils.Error(c, errcode.UnauthorizedTokenTimeout)
			c.Abort()
			return
		}

		// 验证Redis中的token
		cachedToken, err := global.App.Redis.Get(c, app.UserTokenKey(claims.UserId))
		if err != nil || cachedToken != token {
			utils.Error(c, errcode.UnauthorizedTokenError)
			c.Abort()
			return
		}

		c.Set("user_id", claims.UserId)
		c.Set("username", claims.UserName)
		c.Set("is_admin", claims.IsAdmin)
		c.Set("tenant_type", claims.TenantType)
		c.Set("tenant_id", claims.TenantId)
		c.Next()
	}
}
