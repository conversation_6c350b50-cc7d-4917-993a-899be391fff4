package routers

import (
	"ysa-auth/internal/routers/api"
	"ysa-auth/internal/routers/middleware"

	"github.com/gin-gonic/gin"
)

func RegisterAPIRoutes(r *gin.Engine) {
	authController := api.NewAuthController()
	// 登录方法
	r.POST("/login", authController.Login)
	// 获取用户详细信息
	r.GET("/getInfo", middleware.JWT(), authController.GetInfo)
	// 获取路由
	r.GET("/getRouters", middleware.JWT(), authController.GetRouters)
	// 退出方法
	r.POST("/logout", authController.Logout)
	// 注册
	r.POST("/register", middleware.JWT(), authController.Register)

	userController := api.NewUserController()
	userGroup := r.Group("/user").Use(middleware.JWT())
	{
		//查询用户列表
		userGroup.GET("/list", middleware.CheckPermission("system:user:list"), userController.GetList)
		//查询用户详细
		userGroup.GET("/:user_id", middleware.CheckPermission("system:user:query"), userController.GetDetailByUserId)
		//新增用户
		userGroup.POST("", middleware.CheckPermission("system:user:add"), userController.CreateUser)
		//修改用户
		userGroup.PUT("", middleware.CheckPermission("system:user:edit"), userController.UpdateUser)
		//删除用户
		userGroup.DELETE("/:user_id", middleware.CheckPermission("system:user:remove"), userController.DeleteUser)
		//用户密码重置
		userGroup.PUT("/resetPwd", middleware.CheckPermission("system:user:resetPwd"), userController.ResetPassword)
		//用户状态修改
		userGroup.PUT("/changeStatus", middleware.CheckPermission("system:user:edit"), userController.ChangeStatus)
		//查询用户个人信息
		userGroup.GET("/profile", authController.GetProfileByLoginUser)
		//修改用户个人信息
		userGroup.PUT("/profile", authController.UpdateProfileByLoginUser)
		//用户密码重置
		userGroup.PUT("/profile/updatePwd", authController.UpdatePasswordByLoginUser)
		//用户头像上传
		userGroup.POST("/profile/avatar", authController.UploadAvatarByLoginUser)
		//查询授权角色
		userGroup.GET("/authRole/:user_id", middleware.CheckPermission("system:user:query"), userController.GetAuthRoleByUserId)
		//保存授权角色
		userGroup.PUT("/authRole", middleware.CheckPermission("system:user:edit"), userController.SaveAuthRole)
		//查询部门下拉树结构
		userGroup.GET("/deptTree", middleware.CheckPermission("system:user:list"), userController.GetDeptTree)
	}

	deptController := api.NewDeptController()
	deptGroup := r.Group("/dept").Use(middleware.JWT())
	{
		// 查询部门列表
		deptGroup.GET("/list", middleware.CheckPermission("system:dept:list"), deptController.GetList)
		// 查询部门列表（排除节点）
		deptGroup.GET("/list/exclude/:dept_id", middleware.CheckPermission("system:dept:list"), deptController.GetListExcludeChildren)
		// 查询部门详细
		deptGroup.GET("/:dept_id", middleware.CheckPermission("system:dept:query"), deptController.GetDetailByDeptId)
		// 新增部门
		deptGroup.POST("", middleware.CheckPermission("system:dept:add"), deptController.CreateDept)
		// 修改部门
		deptGroup.PUT("", middleware.CheckPermission("system:dept:edit"), deptController.UpdateDept)
		// 删除部门
		deptGroup.DELETE("/:dept_id", middleware.CheckPermission("system:dept:remove"), deptController.DeleteDept)
	}

	postController := api.NewPostController()
	postGroup := r.Group("/post").Use(middleware.JWT())
	{
		//查询岗位列表
		postGroup.GET("/list", middleware.CheckPermission("system:post:list"), postController.GetList)
		//查询岗位详情
		postGroup.GET("/:post_id", middleware.CheckPermission("system:post:query"), postController.GetDetailByPostId)
		//新增岗位
		postGroup.POST("", middleware.CheckPermission("system:post:add"), postController.CreatePost)
		//修改岗位
		postGroup.PUT("", middleware.CheckPermission("system:post:edit"), postController.UpdatePost)
		//删除岗位
		postGroup.DELETE("/:post_id", middleware.CheckPermission("system:post:remove"), postController.DeletePost)
	}

	roleController := api.NewRoleController()
	roleGroup := r.Group("/role").Use(middleware.JWT())
	{
		//查询角色列表
		roleGroup.GET("/list", middleware.CheckPermission("system:role:list"), roleController.GetList)
		//查询角色详细
		roleGroup.GET("/:role_id", middleware.CheckPermission("system:role:query"), roleController.GetDetailByRoleId)
		//新增角色
		roleGroup.POST("", middleware.CheckPermission("system:role:add"), roleController.CreateRole)
		//修改角色
		roleGroup.PUT("", middleware.CheckPermission("system:role:edit"), roleController.UpdateRole)
		//角色数据权限
		roleGroup.PUT("/dataScope", middleware.CheckPermission("system:role:edit"), roleController.UpdateDataScope)
		//角色状态修改
		roleGroup.PUT("/changeStatus", middleware.CheckPermission("system:role:edit"), roleController.ChangeStatus)
		//删除角色
		roleGroup.DELETE("/:role_id", middleware.CheckPermission("system:role:remove"), roleController.DeleteRole)
		//查询角色已授权用户列表
		roleGroup.GET("/authUser/allocatedList", middleware.CheckPermission("system:role:list"), roleController.GetAllocatedList)
		//查询角色未授权用户列表
		roleGroup.GET("/authUser/unallocatedList", middleware.CheckPermission("system:role:list"), roleController.GetUnallocatedList)
		//取消用户授权角色
		roleGroup.PUT("/authUser/cancel", middleware.CheckPermission("system:role:edit"), roleController.CancelAuthUser)
		//批量取消用户授权角色
		roleGroup.PUT("/authUser/cancelAll", middleware.CheckPermission("system:role:edit"), roleController.CancelAllAuthUser)
		//授权用户选择
		roleGroup.PUT("/authUser/selectAll", middleware.CheckPermission("system:role:edit"), roleController.SelectAllAuthUser)
		//根据角色ID查询部门树结构
		roleGroup.GET("/deptTree/:role_id", middleware.CheckPermission("system:role:query"), roleController.GetDeptTreeByRoleId)
	}

	menuController := api.NewMenuController()
	menuGroup := r.Group("/menu").Use(middleware.JWT())
	{
		//查询菜单列表
		menuGroup.GET("/list", middleware.CheckPermission("system:menu:list"), menuController.GetList)
		//查询菜单详细
		menuGroup.GET("/:menu_id", middleware.CheckPermission("system:menu:query"), menuController.GetDetailByMenuId)
		//查询菜单下拉树结构
		menuGroup.GET("/treeselect", menuController.GetTreeSelect)
		//根据角色ID查询菜单下拉树结构
		menuGroup.GET("/roleMenuTreeselect/:role_id", menuController.GetRoleMenuTreeSelect)
		//新增菜单
		menuGroup.POST("", middleware.CheckPermission("system:menu:add"), menuController.CreateMenu)
		//修改菜单
		menuGroup.PUT("", middleware.CheckPermission("system:menu:edit"), menuController.UpdateMenu)
		//删除菜单
		menuGroup.DELETE("/:menu_id", middleware.CheckPermission("system:menu:remove"), menuController.DeleteMenu)
	}

	dictController := api.NewDictController()
	dictGroup := r.Group("/dict").Use(middleware.JWT())
	{
		//查询字典列表
		dictGroup.GET("/data/list", middleware.CheckPermission("system:dict:list"), dictController.GetDataList)
		//查询字典详细
		dictGroup.GET("/data/:dict_code", middleware.CheckPermission("system:dict:query"), dictController.GetDataDetailByDictCode)
		//根据字典类型查询字典数据信息
		dictGroup.GET("/data/type/:dict_type", dictController.GetDictDataByType)
		//新增字典类型
		dictGroup.POST("/data", middleware.CheckPermission("system:dict:add"), dictController.CreateDictData)
		//修改保存字典类型
		dictGroup.PUT("/data", middleware.CheckPermission("system:dict:edit"), dictController.UpdateDictData)
		//删除字典类型
		dictGroup.DELETE("/data/:dict_code", middleware.CheckPermission("system:dict:remove"), dictController.DeleteDictData)

		//查询字典类型列表
		dictGroup.GET("/type/list", middleware.CheckPermission("system:dict:list"), dictController.GetTypeList)
		//查询字典类型详细
		dictGroup.GET("/type/:dict_id", middleware.CheckPermission("system:dict:query"), dictController.GetTypeDetailByDictId)
		//新增字典类型
		dictGroup.POST("/type", middleware.CheckPermission("system:dict:add"), dictController.CreateDictType)
		//修改字典类型
		dictGroup.PUT("/type", middleware.CheckPermission("system:dict:edit"), dictController.UpdateDictType)
		//删除字典类型
		dictGroup.DELETE("/type/:dict_id", middleware.CheckPermission("system:dict:remove"), dictController.DeleteDictType)

		//获取字典选择框列表
		dictGroup.GET("/type/optionselect", dictController.GetDictTypeOptionSelect)
	}

	storeMenuController := api.NewStoreMenuController()
	storeMenuGroup := r.Group("/store-menu").Use(middleware.JWT())
	{
		//查询门店菜单列表
		storeMenuGroup.GET("/list", middleware.CheckPermission("system:store-menu:list"), storeMenuController.GetList)
		//查询门店菜单详细
		storeMenuGroup.GET("/:menu_id", middleware.CheckPermission("system:store-menu:query"), storeMenuController.GetDetailByMenuId)
		//查询门店菜单下拉树结构
		storeMenuGroup.GET("/treeselect", storeMenuController.GetTreeSelect)
		//根据角色ID查询门店菜单下拉树结构
		storeMenuGroup.GET("/roleMenuTreeselect/:role_id", storeMenuController.GetRoleMenuTreeSelect)
		//新增门店菜单
		storeMenuGroup.POST("", middleware.CheckPermission("system:store-menu:add"), storeMenuController.CreateMenu)
		//修改门店菜单
		storeMenuGroup.PUT("", middleware.CheckPermission("system:store-menu:edit"), storeMenuController.UpdateMenu)
		//删除门店菜单
		storeMenuGroup.DELETE("/:menu_id", middleware.CheckPermission("system:store-menu:remove"), storeMenuController.DeleteMenu)
	}
}
