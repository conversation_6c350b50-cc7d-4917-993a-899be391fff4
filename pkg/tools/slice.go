package tools

import (
	"reflect"
	"sort"
	"ysa-auth/pkg/app/resp"
)

// 判断字符串数组中是否包含指定字符串
func InArrayString(arr []string, str string) bool {
	for _, a := range arr {
		if a == str {
			return true
		}
	}
	return false
}

// 判断一个uint64类型的值是否在一个uint64类型的数组中
func InArrayUint64(arr []uint64, str uint64) bool {
	for _, a := range arr {
		if a == str {
			return true
		}
	}
	return false
}

func BuildSliceToTree(node []*resp.TreeResponse, pid uint64) []*resp.TreeResponse {
	res := make([]*resp.TreeResponse, 0)
	for _, v := range node {
		if v.Pid == pid {
			v.Children = BuildSliceToTree(node, v.Id)
			res = append(res, v)
		}
	}
	return res

}

// RemoveDuplicateRows 去除切片中的重复元素, 取字段
// getFieldValue 获取结构体指定字段的值
func getFieldValue(v interface{}, field string) (interface{}, bool) {
	val := reflect.ValueOf(v)

	// 处理指针类型
	if val.Kind() == reflect.Ptr && !val.IsNil() {
		val = val.Elem()
	}

	// 只处理结构体类型
	if val.Kind() == reflect.Struct {
		// 获取指定字段
		fieldVal := val.FieldByName(field)
		if fieldVal.IsValid() {
			return fieldVal.Interface(), true
		}
	}

	return nil, false
}

// RemoveDuplicateRows 去除切片中的重复元素, 取字段
func RemoveDuplicateRows[T any](s []T, field string) []T {
	if len(s) == 0 {
		return s
	}

	// 使用map存储唯一值
	m := make(map[interface{}]T)
	for _, v := range s {
		// 获取字段值
		key, ok := getFieldValue(v, field)
		if ok {
			// 使用字段值作为键
			m[key] = v
		} else {
			// 如果字段不存在，使用整个值作为键
			m[v] = v
		}
	}

	// 构建结果切片
	res := make([]T, 0, len(m))
	for _, v := range m {
		res = append(res, v)
	}

	return res
}

// SortByField 根据指定字段对切片进行排序
func SortByField[T any](s []T, field string) []T {
	if len(s) <= 1 {
		return s
	}

	// 创建副本，避免修改原始切片
	result := make([]T, len(s))
	copy(result, s)

	// 使用sort包进行排序
	sort.Slice(result, func(i, j int) bool {
		// 获取第i个元素的字段值
		valI, okI := getFieldValue(result[i], field)
		// 获取第j个元素的字段值
		valJ, okJ := getFieldValue(result[j], field)

		// 如果任一元素没有指定字段，则保持原顺序
		if !okI || !okJ {
			return i < j
		}

		// 根据字段类型进行比较
		switch valI.(type) {
		case int:
			return valI.(int) < valJ.(int)
		case int64:
			return valI.(int64) < valJ.(int64)
		case uint64:
			return valI.(uint64) < valJ.(uint64)
		case float64:
			return valI.(float64) < valJ.(float64)
		case string:
			return valI.(string) < valJ.(string)
		default:
			// 对于不支持的类型，保持原顺序
			return i < j
		}
	})

	return result
}
