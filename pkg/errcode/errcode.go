package errcode

import "github.com/shrimps80/go-service-utils/utils"

var (
	ServerError               = &utils.ErrorCode{Code: 10000000, Message: "服务内部错误", Type: utils.ErrorTypeBusiness}
	InvalidParams             = &utils.ErrorCode{Code: 10000001, Message: "参数错误", Type: utils.ErrorTypeBusiness}
	NotFound                  = &utils.ErrorCode{Code: 10000002, Message: "找不到", Type: utils.ErrorTypeBusiness}
	Unauthorized              = &utils.ErrorCode{Code: 10000003, Message: "鉴权失败", Type: utils.ErrorTypeBusiness}
	UnauthorizedAuthNotExist  = &utils.ErrorCode{Code: 10000004, Message: "鉴权失败, Token不存在", Type: utils.ErrorTypeBusiness}
	UnauthorizedTokenError    = &utils.ErrorCode{Code: 10000005, Message: "鉴权失败, Token错误", Type: utils.ErrorTypeBusiness}
	UnauthorizedTokenTimeout  = &utils.ErrorCode{Code: 10000006, Message: "鉴权失败, Token超时", Type: utils.ErrorTypeBusiness}
	UnauthorizedTokenGenerate = &utils.ErrorCode{Code: 10000007, Message: "鉴权失败, Token生成失败", Type: utils.ErrorTypeBusiness}
	TooManyRequests           = &utils.ErrorCode{Code: 10000008, Message: "请求过于频繁", Type: utils.ErrorTypeBusiness}
	Forbidden                 = &utils.ErrorCode{Code: 10000009, Message: "禁止访问", Type: utils.ErrorTypeBusiness}
	ErrorNotPlatformTenant    = &utils.ErrorCode{Code: 10000010, Message: "不是平台租户", Type: utils.ErrorTypeBusiness}

	ErrorGetUserListFail     = &utils.ErrorCode{Code: 20020001, Message: "获取用户列表失败", Type: utils.ErrorTypeBusiness}
	ErrorGetUserFail         = &utils.ErrorCode{Code: 20020002, Message: "获取用户失败", Type: utils.ErrorTypeBusiness}
	ErrorCreateUserFail      = &utils.ErrorCode{Code: 20020003, Message: "创建用户失败", Type: utils.ErrorTypeBusiness}
	ErrorUpdateUserFail      = &utils.ErrorCode{Code: 20020004, Message: "更新用户失败", Type: utils.ErrorTypeBusiness}
	ErrorDeleteUserFail      = &utils.ErrorCode{Code: 20020005, Message: "删除用户失败", Type: utils.ErrorTypeBusiness}
	ErrorCountUserFail       = &utils.ErrorCode{Code: 20020006, Message: "统计用户失败", Type: utils.ErrorTypeBusiness}
	ErrorDeleteAuthUserFail  = &utils.ErrorCode{Code: 20020007, Message: "删除用户权限失败", Type: utils.ErrorTypeBusiness}
	ErrorInsertAuthUsersFail = &utils.ErrorCode{Code: 20020008, Message: "插入用户权限失败", Type: utils.ErrorTypeBusiness}
	ErrorInsertAuthRolesFail = &utils.ErrorCode{Code: 20020009, Message: "插入用户角色失败", Type: utils.ErrorTypeBusiness}

	ErrorGetDeptListFail = &utils.ErrorCode{Code: 20020010, Message: "获取部门列表失败", Type: utils.ErrorTypeBusiness}
	ErrorGetDeptFail     = &utils.ErrorCode{Code: 20020011, Message: "获取部门失败", Type: utils.ErrorTypeBusiness}
	ErrorCreateDeptFail  = &utils.ErrorCode{Code: 20020012, Message: "创建部门失败", Type: utils.ErrorTypeBusiness}
	ErrorUpdateDeptFail  = &utils.ErrorCode{Code: 20020013, Message: "更新部门失败", Type: utils.ErrorTypeBusiness}
	ErrorDeleteDeptFail  = &utils.ErrorCode{Code: 20020014, Message: "删除部门失败", Type: utils.ErrorTypeBusiness}
	ErrorCountDeptFail   = &utils.ErrorCode{Code: 20020015, Message: "统计部门失败", Type: utils.ErrorTypeBusiness}
	ErrorDeptTreeFail    = &utils.ErrorCode{Code: 20020016, Message: "获取部门树失败", Type: utils.ErrorTypeBusiness}

	ErrorGetPostListFail = &utils.ErrorCode{Code: 20030001, Message: "获取岗位列表失败", Type: utils.ErrorTypeBusiness}
	ErrorGetPostFail     = &utils.ErrorCode{Code: 20030002, Message: "获取岗位失败", Type: utils.ErrorTypeBusiness}
	ErrorCreatePostFail  = &utils.ErrorCode{Code: 20030003, Message: "创建岗位失败", Type: utils.ErrorTypeBusiness}
	ErrorUpdatePostFail  = &utils.ErrorCode{Code: 20030004, Message: "更新岗位失败", Type: utils.ErrorTypeBusiness}
	ErrorDeletePostFail  = &utils.ErrorCode{Code: 20030005, Message: "删除岗位失败", Type: utils.ErrorTypeBusiness}
	ErrorCountPostFail   = &utils.ErrorCode{Code: 20030006, Message: "统计岗位失败", Type: utils.ErrorTypeBusiness}

	ErrorGetRoleListFail         = &utils.ErrorCode{Code: 20040001, Message: "获取角色列表失败", Type: utils.ErrorTypeBusiness}
	ErrorGetRoleFail             = &utils.ErrorCode{Code: 20040002, Message: "获取角色失败", Type: utils.ErrorTypeBusiness}
	ErrorCreateRoleFail          = &utils.ErrorCode{Code: 20040003, Message: "创建角色失败", Type: utils.ErrorTypeBusiness}
	ErrorUpdateRoleFail          = &utils.ErrorCode{Code: 20040004, Message: "更新角色失败", Type: utils.ErrorTypeBusiness}
	ErrorDeleteRoleFail          = &utils.ErrorCode{Code: 20040005, Message: "删除角色失败", Type: utils.ErrorTypeBusiness}
	ErrorCountRoleFail           = &utils.ErrorCode{Code: 20040006, Message: "统计角色失败", Type: utils.ErrorTypeBusiness}
	ErrorInsertAuthDataScopeFail = &utils.ErrorCode{Code: 20040007, Message: "插入角色数据权限失败", Type: utils.ErrorTypeBusiness}

	ErrorGetRolePermissionListFail = &utils.ErrorCode{Code: 20040008, Message: "获取角色权限列表失败", Type: utils.ErrorTypeBusiness}
	ErrorGetRolePermissionFail     = &utils.ErrorCode{Code: 20040009, Message: "获取角色权限失败", Type: utils.ErrorTypeBusiness}
	ErrorCreateRolePermissionFail  = &utils.ErrorCode{Code: 20040010, Message: "创建角色权限失败", Type: utils.ErrorTypeBusiness}
	ErrorUpdateRolePermissionFail  = &utils.ErrorCode{Code: 20040011, Message: "更新角色权限失败", Type: utils.ErrorTypeBusiness}
	ErrorDeleteRolePermissionFail  = &utils.ErrorCode{Code: 20040012, Message: "删除角色权限失败", Type: utils.ErrorTypeBusiness}
	ErrorCountRolePermissionFail   = &utils.ErrorCode{Code: 20040013, Message: "统计角色权限失败", Type: utils.ErrorTypeBusiness}

	ErrorGetRoutersFail  = &utils.ErrorCode{Code: 20040014, Message: "获取路由失败", Type: utils.ErrorTypeBusiness}
	ErrorGetMenuListFail = &utils.ErrorCode{Code: 20040015, Message: "获取菜单列表失败", Type: utils.ErrorTypeBusiness}
	ErrorGetMenuFail     = &utils.ErrorCode{Code: 20040016, Message: "获取菜单失败", Type: utils.ErrorTypeBusiness}
	ErrorDeleteMenuFail  = &utils.ErrorCode{Code: 20040017, Message: "删除菜单失败", Type: utils.ErrorTypeBusiness}
	ErrorUpdateMenuFail  = &utils.ErrorCode{Code: 20040018, Message: "更新菜单失败", Type: utils.ErrorTypeBusiness}
	ErrorCreateMenuFail  = &utils.ErrorCode{Code: 20040019, Message: "创建菜单失败", Type: utils.ErrorTypeBusiness}

	ErrorGetDictDataListFail = &utils.ErrorCode{Code: 20040020, Message: "获取字典数据列表失败", Type: utils.ErrorTypeBusiness}
	ErrorGetDictDataFail     = &utils.ErrorCode{Code: 20040021, Message: "获取字典数据失败", Type: utils.ErrorTypeBusiness}
	ErrorCreateDictDataFail  = &utils.ErrorCode{Code: 20040022, Message: "创建字典数据失败", Type: utils.ErrorTypeBusiness}
	ErrorUpdateDictDataFail  = &utils.ErrorCode{Code: 20040023, Message: "更新字典数据失败", Type: utils.ErrorTypeBusiness}
	ErrorDeleteDictDataFail  = &utils.ErrorCode{Code: 20040024, Message: "删除字典数据失败", Type: utils.ErrorTypeBusiness}

	ErrorGetDictTypeListFail = &utils.ErrorCode{Code: 20040025, Message: "获取字典类型列表失败", Type: utils.ErrorTypeBusiness}
	ErrorGetDictTypeFail     = &utils.ErrorCode{Code: 20040026, Message: "获取字典类型失败", Type: utils.ErrorTypeBusiness}
	ErrorCreateDictTypeFail  = &utils.ErrorCode{Code: 20040027, Message: "创建字典类型失败", Type: utils.ErrorTypeBusiness}
	ErrorUpdateDictTypeFail  = &utils.ErrorCode{Code: 20040028, Message: "更新字典类型失败", Type: utils.ErrorTypeBusiness}
	ErrorDeleteDictTypeFail  = &utils.ErrorCode{Code: 20040029, Message: "删除字典类型失败", Type: utils.ErrorTypeBusiness}
)
