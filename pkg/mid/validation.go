package mid

import (
	"reflect"
	"strings"

	"github.com/gin-gonic/gin"
	"github.com/gin-gonic/gin/binding"
	"github.com/go-playground/locales/zh"
	ut "github.com/go-playground/universal-translator"
	"github.com/go-playground/validator/v10"
	zh_translations "github.com/go-playground/validator/v10/translations/zh"
	"github.com/shrimps80/go-service-utils/utils"
)

var (
	trans ut.Translator // 全局翻译器实例
)

// ValidationError 自定义验证错误类型
type ValidationError struct {
	Message string      `json:"message"`
	Field   string      `json:"field"`
	Tag     string      `json:"tag"`
	Value   interface{} `json:"value"`
}

// InitValidator 初始化验证器和翻译器
func InitValidator() {
	if v, ok := binding.Validator.Engine().(*validator.Validate); ok {
		// 注册字段名处理函数，优先使用label标签，其次使用json标签
		v.RegisterTagNameFunc(func(fld reflect.StructField) string {
			// 优先使用label标签作为字段名
			if label := fld.Tag.Get("label"); label != "" {
				return label
			}
			// 其次使用json标签
			if jsonTag := fld.Tag.Get("json"); jsonTag != "" {
				name := strings.SplitN(jsonTag, ",", 2)[0]
				if name != "-" {
					return name
				}
			}
			// 最后使用字段名
			return fld.Name
		})

		// 初始化中文翻译器
		zh := zh.New()
		uni := ut.New(zh, zh)
		trans, _ = uni.GetTranslator("zh")

		// 注册默认翻译
		_ = zh_translations.RegisterDefaultTranslations(v, trans)
	}
}

// ValidationMiddleware 验证中间件
func ValidationMiddleware() gin.HandlerFunc {
	return func(c *gin.Context) {
		// 仅处理需要验证的请求
		if c.Request.Method == "POST" || c.Request.Method == "PUT" || c.Request.Method == "PATCH" {
			// 在实际业务处理中自动验证，此处只设置错误处理
			c.Next()

			// 检查是否存在验证错误
			if len(c.Errors) > 0 {
				var validationErrors validator.ValidationErrors
				for _, ginErr := range c.Errors {
					if errs, ok := ginErr.Err.(validator.ValidationErrors); ok {
						validationErrors = errs
						break
					}
				}

				// 转换验证错误信息
				if validationErrors != nil {
					errMessages := make(map[string]string)
					for _, err := range validationErrors {
						errMessages[err.Field()] = err.Translate(trans)
					}

					// 使用统一的错误响应格式
					utils.Error(c, utils.ErrInvalidParam, utils.WithMessage("请求参数验证失败"))
					return
				}
			}
		}
		c.Next()
	}
}

// ShouldBindWithValidation 带自动验证的绑定方法
func ShouldBindWithValidation(c *gin.Context, obj interface{}) error {
	if err := c.ShouldBind(obj); err != nil {
		if validationErrors, ok := err.(validator.ValidationErrors); ok {
			// 获取第一个验证错误的中文信息
			if len(validationErrors) > 0 {
				firstError := validationErrors[0]
				chineseMsg := firstError.Translate(trans)

				// 创建一个包含中文错误信息的自定义错误
				return &ValidationError{
					Message: chineseMsg,
					Field:   firstError.Field(),
					Tag:     firstError.Tag(),
					Value:   firstError.Value(),
				}
			}
		}
		return err
	}
	return nil
}

func (e *ValidationError) Error() string {
	return e.Message
}
