package mid

import (
	"reflect"
	"strings"

	"github.com/gin-gonic/gin"
	"github.com/gin-gonic/gin/binding"
	"github.com/go-playground/locales/zh"
	ut "github.com/go-playground/universal-translator"
	"github.com/go-playground/validator/v10"
	zh_translations "github.com/go-playground/validator/v10/translations/zh"
	"github.com/shrimps80/go-service-utils/utils"
)

var (
	trans ut.Translator // 全局翻译器实例
)

// InitValidator 初始化验证器和翻译器
func InitValidator() {
	if v, ok := binding.Validator.Engine().(*validator.Validate); ok {
		// 注册字段名处理函数，优先使用label标签，其次使用json标签
		v.RegisterTagNameFunc(func(fld reflect.StructField) string {
			// 优先使用label标签作为字段名
			if label := fld.Tag.Get("label"); label != "" {
				return label
			}
			// 其次使用json标签
			if jsonTag := fld.Tag.Get("json"); jsonTag != "" {
				name := strings.SplitN(jsonTag, ",", 2)[0]
				if name != "-" {
					return name
				}
			}
			// 最后使用字段名
			return fld.Name
		})

		// 初始化中文翻译器
		zh := zh.New()
		uni := ut.New(zh, zh)
		trans, _ = uni.GetTranslator("zh")

		// 注册默认翻译
		_ = zh_translations.RegisterDefaultTranslations(v, trans)

		// 注册自定义验证规则和翻译
		registerCustomValidations(v)
		registerCustomTranslations(v)
	}
}

// ValidationMiddleware 验证中间件
func ValidationMiddleware() gin.HandlerFunc {
	return func(c *gin.Context) {
		// 仅处理需要验证的请求
		if c.Request.Method == "POST" || c.Request.Method == "PUT" || c.Request.Method == "PATCH" {
			// 在实际业务处理中自动验证，此处只设置错误处理
			c.Next()

			// 检查是否存在验证错误
			if len(c.Errors) > 0 {
				var validationErrors validator.ValidationErrors
				for _, ginErr := range c.Errors {
					if errs, ok := ginErr.Err.(validator.ValidationErrors); ok {
						validationErrors = errs
						break
					}
				}

				// 转换验证错误信息
				if validationErrors != nil {
					errMessages := make(map[string]string)
					for _, err := range validationErrors {
						errMessages[err.Field()] = err.Translate(trans)
					}

					// 使用统一的错误响应格式
					utils.Error(c, utils.ErrInvalidParam, utils.WithMessage("请求参数验证失败"))
					return
				}
			}
		}
		c.Next()
	}
}

// registerCustomValidations 注册自定义验证规则
func registerCustomValidations(v *validator.Validate) {
	// 示例：手机号验证
	v.RegisterValidation("mobile", func(fl validator.FieldLevel) bool {
		value := fl.Field().String()
		return len(value) == 11 && strings.HasPrefix(value, "1")
	})
}

// registerCustomTranslations 注册自定义翻译
func registerCustomTranslations(v *validator.Validate) {
	// 注册手机号验证的翻译
	_ = v.RegisterTranslation("mobile", trans, func(ut ut.Translator) error {
		return ut.Add("mobile", "{0}格式不正确", true)
	}, func(ut ut.Translator, fe validator.FieldError) string {
		t, _ := ut.T("mobile", fe.Field())
		return t
	})

	// 重新注册常用验证规则的中文翻译，使用更友好的格式
	translations := []struct {
		tag         string
		translation string
		override    bool
	}{
		{"required", "{0}不能为空", true},
		{"min", "{0}长度不能少于{1}位", true},
		{"max", "{0}长度不能超过{1}位", true},
		{"len", "{0}长度必须为{1}位", true},
		{"email", "{0}格式不正确", true},
		{"numeric", "{0}必须为数字", true},
		{"alpha", "{0}只能包含字母", true},
		{"alphanum", "{0}只能包含字母和数字", true},
		{"gte", "{0}必须大于或等于{1}", true},
		{"lte", "{0}必须小于或等于{1}", true},
		{"gt", "{0}必须大于{1}", true},
		{"lt", "{0}必须小于{1}", true},
		{"eq", "{0}必须等于{1}", true},
		{"ne", "{0}不能等于{1}", true},
		{"oneof", "{0}必须是[{1}]中的一个", true},
	}

	for _, t := range translations {
		// 注册翻译
		_ = v.RegisterTranslation(t.tag, trans, func(ut ut.Translator) error {
			return ut.Add(t.tag, t.translation, t.override)
		}, func(ut ut.Translator, fe validator.FieldError) string {
			t, _ := ut.T(t.tag, fe.Field(), fe.Param())
			return t
		})
	}
}

// ShouldBindWithValidation 带自动验证的绑定方法
func ShouldBindWithValidation(c *gin.Context, obj interface{}) error {
	if err := c.ShouldBind(obj); err != nil {
		if validationErrors, ok := err.(validator.ValidationErrors); ok {
			// 获取第一个验证错误的中文信息
			if len(validationErrors) > 0 {
				firstError := validationErrors[0]
				chineseMsg := firstError.Translate(trans)

				// 创建一个包含中文错误信息的自定义错误
				return &ValidationError{
					Message: chineseMsg,
					Field:   firstError.Field(),
					Tag:     firstError.Tag(),
					Value:   firstError.Value(),
				}
			}
		}
		return err
	}
	return nil
}

// ValidationError 自定义验证错误类型
type ValidationError struct {
	Message string      `json:"message"`
	Field   string      `json:"field"`
	Tag     string      `json:"tag"`
	Value   interface{} `json:"value"`
}

func (e *ValidationError) Error() string {
	return e.Message
}
