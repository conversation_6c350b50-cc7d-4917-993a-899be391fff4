package resp

type TreeMenuResponse struct {
	MenuId      uint64                  `json:"-"`
	Name        string                  `json:"name"`
	Path        string                  `json:"path"`
	Hidden      bool                    `json:"hidden"`
	Redirect    string                  `json:"redirect"`
	Component   string                  `json:"component"`
	AlwaysShow  bool                    `json:"alwaysShow"`
	Meta        *TreeMetaResponse       `json:"meta"`
	Children    []*TreeChildrenResponse `json:"children"`
	ChildrenMap []*TreeMenuResponse     `json:"-"`
}

type TreeMetaResponse struct {
	Title   string `json:"title"`
	Icon    string `json:"icon"`
	NoCache bool   `json:"noCache"`
	Link    string `json:"link"`
}

type TreeChildrenResponse struct {
	Name      string            `json:"name"`
	Path      string            `json:"path"`
	Hidden    bool              `json:"hidden"`
	Component string            `json:"component"`
	Meta      *TreeMetaResponse `json:"meta"`
}

type MenuResponse struct {
	CreateTime string `json:"createTime"`
	Remark     string `json:"remark"`
	MenuId     uint64 `json:"menuId"`
	MenuName   string `json:"menuName"`
	ParentId   uint64 `json:"parentId"`
	OrderNum   int    `json:"orderNum"`
	Path       string `json:"path"`
	Component  string `json:"component"`
	Query      string `json:"query"`
	RouteName  string `json:"routeName"`
	IsFrame    string `json:"isFrame"`
	IsCache    string `json:"isCache"`
	MenuType   string `json:"menuType"`
	Visible    string `json:"visible"`
	Status     string `json:"status"`
	Perms      string `json:"perms"`
	Icon       string `json:"icon"`
}

type TreeResponse struct {
	Id       uint64          `json:"id"`
	Pid      uint64          `json:"-"`
	Label    string          `json:"label"`
	Disabled bool            `json:"disabled"`
	Children []*TreeResponse `json:"children"`
}
