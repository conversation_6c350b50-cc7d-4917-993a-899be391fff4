package req

type PostListRequest struct {
	PostCode   string `form:"postCode"`
	PostName   string `form:"postName"`
	Status     string `form:"status"`
	TenantType string
	TenantId   uint64
}

type CreatePostRequest struct {
	PostCode   string `form:"post_code" binding:"required" label:"岗位编码"`
	PostName   string `form:"post_name" binding:"required" label:"岗位名称"`
	PostSort   int    `form:"post_sort" binding:"gte=0" label:"显示顺序"`
	Status     string `form:"status" binding:"required" label:"状态"`
	Remark     string `form:"remark"`
	Operater   string
	TenantType string
	TenantId   uint64
}

type UpdatePostRequest struct {
	PostId     uint64 `form:"postId" binding:"required,gte=1" label:"岗位id"`
	PostCode   string `form:"postCode" binding:"required" label:"岗位编码"`
	PostName   string `form:"postName" binding:"required" label:"岗位名称"`
	PostSort   int    `form:"postSort" binding:"gte=0" label:"显示顺序"`
	Status     string `form:"status" binding:"required" label:"状态"`
	Remark     string `form:"remark"`
	Operater   string
	TenantType string
	TenantId   uint64
}
