package req

type PostListRequest struct {
	PostCode   string `form:"postCode"`
	PostName   string `form:"postName"`
	Status     string `form:"status"`
	TenantType string
	TenantId   uint64
}

type CreatePostRequest struct {
	PostCode   string `form:"post_code" binding:"required"`
	PostName   string `form:"post_name" binding:"required"`
	PostSort   int    `form:"post_sort" binding:"gte=0"`
	Status     string `form:"status" binding:"required"`
	Remark     string `form:"remark"`
	Operater   string
	TenantType string
	TenantId   uint64
}

type UpdatePostRequest struct {
	PostId     uint64 `form:"postId" binding:"required,gte=1"`
	PostCode   string `form:"postCode" binding:"required"`
	PostName   string `form:"postName" binding:"required"`
	PostSort   int    `form:"postSort" binding:"gte=0"`
	Status     string `form:"status" binding:"required"`
	Remark     string `form:"remark"`
	Operater   string
	TenantType string
	TenantId   uint64
}
