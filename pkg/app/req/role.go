package req

type RoleListRequest struct {
	RoleId     uint64 `form:"role_id"`
	RoleName   string `form:"roleName"`
	Status     string `form:"status"`
	RoleKey    string `form:"roleKey"`
	BeginTime  string `form:"params[beginTime]"`
	EndTime    string `form:"params[endTime]"`
	TenantType string
	TenantId   uint64
}

type CreateRoleRequest struct {
	RoleName          string `form:"roleName" binding:"required" label:"角色名称"`
	RoleKey           string `form:"roleKey" binding:"required" label:"角色权限字符串"`
	RoleSort          int    `form:"roleSort" binding:"gte=0" label:"显示顺序"`
	DeptCheckStrictly bool   `form:"deptCheckStrictly"`
	DeptIds           string `form:"deptIds"`
	MenuCheckStrictly bool   `form:"menuCheckStrictly"`
	MenuIds           string `form:"menuIds"`
	Status            string `form:"status" binding:"required" label:"角色状态"`
	Remark            string `form:"remark"`
	Operater          string
	TenantType        string
	TenantId          uint64
}

type UpdateRoleRequest struct {
	RoleId            uint64 `form:"roleId" binding:"required" label:"角色id"`
	RoleName          string `form:"roleName" binding:"required" label:"角色名称"`
	RoleKey           string `form:"roleKey" binding:"required" label:"角色权限字符串"`
	RoleSort          int    `form:"roleSort" binding:"gte=0" label:"显示顺序"`
	DeptCheckStrictly bool   `form:"deptCheckStrictly" `
	DeptIds           string `form:"deptIds"`
	MenuCheckStrictly bool   `form:"menuCheckStrictly"`
	MenuIds           string `form:"menuIds"`
	Status            string `form:"status" binding:"required" label:"角色状态"`
	Remark            string `form:"remark"`
	Operater          string
	TenantType        string
	TenantId          uint64
}

type ChangeRoleStatusRequest struct {
	RoleId     uint64 `form:"roleId" binding:"required,gte=1" label:"角色id"`
	Status     string `form:"status,default=0" binding:"oneof=0 1" label:"状态"`
	Operater   string
	TenantType string
	TenantId   uint64
}

type AuthDataScopeRequest struct {
	RoleId            uint64 `form:"roleId" binding:"required,gte=1" label:"角色id"`
	DataScope         string `form:"dataScope" binding:"required,gte=1" label:"数据范围"`
	DeptCheckStrictly bool   `form:"deptCheckStrictly"`
	DeptIds           string `form:"deptIds"`
	TenantType        string
	TenantId          uint64
}
