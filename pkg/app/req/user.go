package req

type UserListRequest struct {
	UserId      uint64 `form:"user_id" `
	UserName    string `form:"userName" binding:"max=100"`
	PhoneNumber string `form:"phonenumber" binding:"max=100"`
	Status      string `form:"status,default=0" binding:"oneof=0 1"`
	BeginTime   string `form:"params[beginTime]"`
	EndTime     string `form:"params[endTime]"`
	DeptId      uint64 `form:"deptId" label:"归属部门"`
	TenantType  string
	TenantId    uint64
}

type RegisterRequest struct {
	UserName string `form:"user_name" binding:"required,min=2,max=100"  label:"用户名称"`
	Password string `form:"password" binding:"required,min=6,max=20"  label:"用户密码"`
	NickName string `form:"nick_name" binding:"required,min=2,max=100"  label:"昵称"`
	Linkman  string `form:"linkman" binding:"required,min=2,max=100"  label:"联系人"`
	Phone    string `form:"phone" binding:"required,max=11"  label:"手机号码"`
	Type     string `form:"type" binding:"required,oneof=store"  label:"注册类型"`
	TypeId   uint64 `form:"type_id" binding:"required,gte=1"  label:"注册类型ID"`
	Operater string
}

type CreateUserRequest struct {
	NickName    string `form:"nickName" binding:"required,min=2,max=100"  label:"用户昵称"`
	DeptId      uint64 `form:"deptId" binding:""  label:"归属部门"`
	PhoneNumber string `form:"phonenumber" binding:"required,max=11"  label:"手机号码"`
	Email       string `form:"email" label:"邮箱"`
	UserName    string `form:"userName" binding:"required,min=2,max=100" label:"用户名称"`
	Avatar      string `form:"avatar" binding:"max=255" label:"头像"`
	Password    string `form:"password" binding:"required,min=6,max=20" label:"用户密码"`
	Sex         string `form:"sex,default=0" binding:"oneof=0 1" label:"用户性别"`
	Status      string `form:"status,default=0" binding:"oneof=0 1" label:"状态"`
	PostIds     string `form:"postIds" binding:"" label:"岗位"`
	RoleIds     string `form:"roleIds" binding:"" label:"角色"`
	Remark      string `form:"remark" binding:"max=255" label:"备注"`
	Operater    string
	TenantType  string
	TenantId    uint64
}

type UpdateUserRequest struct {
	UserId      uint64 `form:"userId" binding:"required,gte=1"`
	NickName    string `form:"nickName" label:"用户昵称"`
	DeptId      uint64 `form:"deptId" label:"归属部门"`
	PhoneNumber string `form:"phonenumber" label:"手机号码"`
	Email       string `form:"email" label:"邮箱"`
	UserName    string `form:"userName" label:"用户名称"`
	Avatar      string `form:"avatar" binding:"max=255" label:"头像"`
	Password    string `form:"password" label:"用户密码"`
	Sex         string `form:"sex,default=0" label:"用户性别"`
	Status      string `form:"status,default=0" label:"状态"`
	PostIds     string `form:"postIds" label:"岗位"`
	RoleIds     string `form:"roleIds" label:"角色"`
	Remark      string `form:"remark" binding:"max=255" label:"备注"`
	Operater    string
	TenantType  string
	TenantId    uint64
}

type UpdateUserProfileRequest struct {
	UserId      uint64
	NickName    string `form:"nickName" binding:"required,min=2,max=100"  label:"用户昵称"`
	Email       string `form:"email" binding:"email,max=100"  label:"邮箱"`
	PhoneNumber string `form:"phonenumber" binding:"required,max=11"  label:"手机号码"`
	Sex         string `form:"sex,default=0" binding:"oneof=0 1" label:"用户性别"`
	Operater    string
	TenantType  string
	TenantId    uint64
}

type ResetPwdRequest struct {
	UserId     uint64 `form:"user_id" binding:"required,gte=1" label:"用户id"`
	Password   string `form:"password" binding:"required,min=6,max=20" label:"用户密码"`
	Operater   string
	TenantType string
	TenantId   uint64
}

type ChangeUserStatusRequest struct {
	UserId     uint64 `form:"user_id" binding:"required,gte=1" label:"用户id"`
	Status     string `form:"status,default=0" binding:"oneof=0 1" label:"状态"`
	Operater   string
	TenantType string
	TenantId   uint64
}

type ChangeNewPwdRequest struct {
	UserId      uint64
	OldPassword string `form:"oldPassword" binding:"required,min=6,max=20" label:"旧密码"`
	NewPassword string `form:"newPassword" binding:"required,min=6,max=20" label:"新密码"`
	ConfirmPwd  string `form:"confirmPwd" binding:"required,min=6,max=20,eqfield=NewPassword" label:"确认密码"`
	Operater    string
	TenantType  string
	TenantId    uint64
}

type ChangeAvatarRequest struct {
	UserId     uint64
	Avatar     string `form:"avatar" binding:"required" label:"头像"`
	Operater   string
	TenantType string
	TenantId   uint64
}

type SelectRoleUserRequest struct {
	UserName    string `form:"userName" binding:"max=100" label:"用户名称"`
	PhoneNumber string `form:"phonenumber" binding:"max=11"  label:"手机号码"`
	RoleId      uint64 `form:"roleId" binding:"gte=1" label:"角色id"`
	TenantType  string
	TenantId    uint64
}

type DeleteAuthUserRequest struct {
	UserId     uint64 `form:"userId" binding:"required,gte=1" label:"用户id"`
	RoleId     uint64 `form:"roleId" binding:"required,gte=1" label:"角色id"`
	TenantType string
	TenantId   uint64
}

type AuthUsersRequest struct {
	RoleId     uint64 `form:"roleId" binding:"required,gte=1" label:"角色id"`
	UserIds    string `form:"userIds" binding:"required" label:"用户ids"`
	TenantType string
	TenantId   uint64
}

type AuthRolesRequest struct {
	UserId     uint64 `form:"userId" binding:"required,gte=1" label:"用户id"`
	RoleIds    string `form:"roleIds" binding:"required" label:"角色ids"`
	TenantType string
	TenantId   uint64
}
