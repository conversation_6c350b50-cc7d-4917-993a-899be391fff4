package req

type DeptListRequest struct {
	DeptName   string `form:"deptName" label:"部门名称"`
	Status     string `form:"status" label:"部门状态"`
	TenantType string
	TenantId   uint64
}

type CreateDeptRequest struct {
	ParentId   uint64 `form:"parent_id" binding:"gte=0"  label:"归属部门"`
	DeptName   string `form:"dept_name" binding:"required"  label:"部门名称"`
	OrderNum   int    `form:"order_num" binding:"gte=0"  label:"显示顺序"`
	Leader     string `form:"leader" label:"负责人"`
	Phone      string `form:"phone" label:"联系电话"`
	Email      string `form:"email" label:"邮箱"`
	Status     string `form:"status" binding:"required"  label:"部门状态"`
	Operater   string
	TenantType string
	TenantId   uint64
}

type UpdateDeptRequest struct {
	DeptId     uint64 `form:"dept_id" binding:"required,gte=1"`
	ParentId   uint64 `form:"parent_id" binding:"gte=0"  label:"归属部门"`
	DeptName   string `form:"dept_name" binding:"required"  label:"部门名称"`
	OrderNum   int    `form:"order_num" binding:"gte=0"  label:"显示顺序"`
	Leader     string `form:"leader" label:"负责人"`
	Phone      string `form:"phone" label:"联系电话"`
	Email      string `form:"email" label:"邮箱"`
	Status     string `form:"status" binding:"required"  label:"部门状态"`
	Operater   string
	TenantType string
	TenantId   uint64
}
