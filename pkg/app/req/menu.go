package req

type MenuListRequest struct {
	MenuId   uint64 `form:"menuId"`
	ParentId uint64 `form:"parentId"`
	MenuName string `form:"menuName"`
	Visible  string `form:"visible"`
	Status   string `form:"status"`
	// UserId   uint64 `form:"user_id" binding:"required,gt=0"`
	TenantType string
	TenantId   uint64
}

type CreateMenuRequest struct {
	MenuName   string `form:"menuName"`
	ParentId   uint64 `form:"parentId" binding:"gte=0"  label:"父菜单ID"`
	OrderNum   int    `form:"orderNum" binding:"gte=0"  label:"显示顺序"`
	Path       string `form:"path"`
	Component  string `form:"component"`
	Query      string `form:"query"`
	RouteName  string `form:"routeName"`
	IsFrame    string `form:"isFrame"  label:"是否为外链（0是 1否）"`
	IsCache    string `form:"isCache"  label:"是否缓存（0缓存 1不缓存）"`
	MenuType   string `form:"menuType"`
	Visible    string `form:"visible"`
	Status     string `form:"status"`
	Perms      string `form:"perms"`
	Icon       string `form:"icon"`
	Remark     string `form:"remark"`
	Operater   string
	TenantType string
	TenantId   uint64
}

type UpdateMenuRequest struct {
	MenuId     uint64 `form:"menu_id" binding:"required,gte=0" label:"菜单ID"`
	MenuName   string `form:"menuName"`
	ParentId   uint64 `form:"parentId" binding:"gte=0"  label:"父菜单ID"`
	OrderNum   int    `form:"orderNum" binding:"gte=0"  label:"显示顺序"`
	Path       string `form:"path"`
	Component  string `form:"component"`
	Query      string `form:"query"`
	RouteName  string `form:"routeName"`
	IsFrame    string `form:"isFrame"  label:"是否为外链（0是 1否）"`
	IsCache    string `form:"isCache"  label:"是否缓存（0缓存 1不缓存）"`
	MenuType   string `form:"menuType"`
	Visible    string `form:"visible"`
	Status     string `form:"status"`
	Perms      string `form:"perms"`
	Icon       string `form:"icon"`
	Remark     string `form:"remark"`
	Operater   string
	TenantType string
	TenantId   uint64
}
