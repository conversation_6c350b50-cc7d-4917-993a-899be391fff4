package req

type DictDataListRequest struct {
	DictType   string `form:"dictType"`
	DictLabel  string `form:"dictLabel"`
	Status     string `form:"status"`
	TenantType string
	TenantId   uint64
}

type DictTypeListRequest struct {
	DictName   string `form:"dictName"`
	DictType   string `form:"dictType"`
	Status     string `form:"status"`
	BeginTime  string `form:"params[beginTime]"`
	EndTime    string `form:"params[endTime]"`
	TenantType string
	TenantId   uint64
}

type CreateDictTypeRequest struct {
	DictName   string `form:"dictName"`
	DictType   string `form:"dictType"`
	Status     string `form:"status"`
	Remark     string `form:"remark"`
	Operater   string
	TenantType string
	TenantId   uint64
}

type UpdateDictTypeRequest struct {
	DictId     uint64 `form:"dictId" binding:"required" label:"字典类型ID"`
	DictName   string `form:"dictName"`
	DictType   string `form:"dictType"`
	Status     string `form:"status"`
	Remark     string `form:"remark"`
	Operater   string
	TenantType string
	TenantId   uint64
}

type CreateDictDataRequest struct {
	CssClass   string `form:"cssClass"`
	DictLabel  string `form:"dictLabel"`
	DictSort   int    `form:"dictSort"`
	DictType   string `form:"dictType"`
	DictValue  string `form:"dictValue"`
	ListClass  string `form:"listClass"`
	Status     string `form:"status"`
	Remark     string `form:"remark"`
	Operater   string
	TenantType string
	TenantId   uint64
}

type UpdateDictDataRequest struct {
	DictCode   uint64 `form:"dictCode" binding:"required" label:"字典数据ID"`
	CssClass   string `form:"cssClass"`
	DictLabel  string `form:"dictLabel"`
	DictSort   int    `form:"dictSort"`
	DictType   string `form:"dictType"`
	DictValue  string `form:"dictValue"`
	ListClass  string `form:"listClass"`
	Status     string `form:"status"`
	Remark     string `form:"remark"`
	Operater   string
	TenantType string
	TenantId   uint64
}
