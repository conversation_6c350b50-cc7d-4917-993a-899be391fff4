package app

import (
	"time"
	"ysa-auth/global"
	"ysa-auth/pkg/tools/convert"

	"github.com/gin-gonic/gin"
)

// Pager 基础分页结构体，用于传递分页参数
type Pager struct {
	Page     int `json:"pageNum"`
	PageSize int `json:"pageSize"`
}

// PagerResp 分页结果结构体，用于API响应
type PagerResp struct {
	Rows     interface{} `json:"rows"`      // 分页数据
	Total    int64       `json:"total"`     // 总记录数
	Page     int         `json:"page"`      // 当前页码
	PageSize int         `json:"page_size"` // 每页大小
}

// LastTimePager 时间分页
type LastTimePager struct {
	LastTime interface{} `json:"lastTime"`
	PageSize int         `json:"pageSize"`
}

type TimePagerResp struct {
	Rows interface{} `json:"rows"` // 分页数据
}

// 用于在DAO层进行查询时指定分页方式和参数
type PageOption struct {
	Page      int         // 当前页码，传统分页使用
	PageSize  int         // 每页记录数
	OrderBy   string      // 排序字段和方式，如 "created_at DESC"
	TimeOrder bool        // 是否使用基于时间的分页（限制条数）
	UseLastID bool        // 是否使用基于ID的分页（游标分页）
	LastID    uint64      // 上次查询的最后ID
	LastTime  interface{} // 上次查询的最后时间，可以是time.Time或string
	NeedTotal bool        // 是否需要获取总记录数
}

// NewPageOption 创建默认的分页选项
func NewPageOption() *PageOption {
	return &PageOption{
		Page:      1,
		PageSize:  10,
		TimeOrder: false,
		UseLastID: false,
		NeedTotal: true,
	}
}

// GetPage 从Gin上下文中获取页码参数
func GetPage(c *gin.Context) int {
	page := convert.StrTo(c.Query("pageNum")).MustInt()
	if page <= 0 {
		return 1
	}

	return page
}

// GetLastTime 函数用于获取请求参数中的最后时间
func GetLastTime(c *gin.Context) time.Time {
	// 从请求参数中获取lastTime参数
	lastTimeStr := c.Query("lastTime")
	// 如果lastTime参数为空，则返回空时间
	if lastTimeStr == "" {
		return time.Time{}
	}

	// 尝试解析Unix时间戳
	timestamp := convert.StrTo(lastTimeStr).MustInt64()
	// 如果解析成功，则返回对应的时间
	if timestamp > 0 {
		return time.Unix(timestamp, 0)
	}

	// 如果不是时间戳，尝试解析日期时间字符串
	lastTime, err := time.Parse("2006-01-02 15:04:05", lastTimeStr)
	// 如果解析失败，则返回空时间
	if err != nil {
		return time.Time{}
	}

	// 返回解析后的时间
	return lastTime
}

// GetPageSize 从Gin上下文中获取每页大小参数，并进行限制
func GetPageSize(c *gin.Context) int {
	pageSize := convert.StrTo(c.Query("pageSize")).MustInt()
	if pageSize <= 0 {
		return global.App.Config.GetInt("App.DefaultPageSize")
	}
	if pageSize > global.App.Config.GetInt("App.MaxPageSize") {
		return global.App.Config.GetInt("App.MaxPageSize")
	}

	return pageSize
}

// GetPageOffset 计算分页偏移量
func GetPageOffset(page, pageSize int) int {
	result := 0
	if page > 0 {
		result = (page - 1) * pageSize
	}

	return result
}
