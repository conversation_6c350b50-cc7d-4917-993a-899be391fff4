## 权限管理服务

考虑到不同层级（集团 -> 总部 -> 合伙人 -> 门店）的权限管理，我们需要设计一个多层级的权限控制系统。这种设计的核心是基于组织层级的用户、角色和权限的分配，并根据不同层级的需求提供适当的权限管理。

### 设计思路
+ 多层级管理：支持集团、总部、合伙人、门店等多层级的管理。
+ 灵活的权限控制：每个层级的管理员、员工和用户可以根据其角色和组织层级来执行相应的权限。
+ 精细化权限分配：可以针对不同的组织层级（如集团、总部、合伙人、门店）进行角色和权限的细化管理。
  
### 组织层级图
```
集团
 ├── 总部
 │    ├── 合伙人
 │    │    ├── 门店
 │    │    └── ...
 │    ├── 门店
 │    └── ...
 └── ...
```

### 权限管理流程
+ 组织层级：每个组织层级（如集团、总部、合伙人、门店）都有独立的权限控制。集团权限可以覆盖所有层级，而门店权限只能覆盖本层级。
+ 用户与角色：用户通过 user_roles 表与其角色关联，而角色的权限通过 role_permissions 表来定义。用户会根据自己所处的组织层级和角色，拥有不同的权限。
+ 权限控制：在实际操作中，系统会根据用户的 organization_level_id 和角色的 organization_level_id 来限制其可以访问的数据和执行的操作。
+ 操作日志：每个操作都会记录在操作日志中，以便审计和跟踪。

### 总结：
+ 需要先创建菜单、权限列表
+ 然后给对应角色分配对应菜单、权限
+ 最后给用户添加对应角色，每个用户对应的菜单、权限就会不同