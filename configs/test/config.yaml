Server:
  Name: ysa-auth
  Version: v1.0.0
  RunMode: release
  HttpPort: 8000
  ReadTimeout: 60
  WriteTimeout: 60

App:
  DefaultPageSize: 10
  MaxPageSize: 100
  DefaultContextTimeout: 60

Log:
  SavePath: storage/logs
  FileName: app
  FileExt: .log
  MaxSize: 100w
  MaxBackups: 10
  MaxAge: 30
  Compress: true
  Level: info

Database:
  DBType: mysql
  Username: ysa_devops
  Password: yishengai@20250427.
  Host: **********:3306
  DBName: ysa-auth
  TablePrefix: 
  Charset: utf8
  ParseTime: True
  MaxIdleConns: 10
  MaxOpenConns: 306
  MaxLifetime: 100
  debug: true

Redis:
  Host: **********:6379
  Password: yishengai@20240912.
  DB: 1
  PoolSize: 100
  MinIdleConns: 10

JWT:
  Secret: eddycjy
  Issuer: ysa-auth
  Expire: 7200
